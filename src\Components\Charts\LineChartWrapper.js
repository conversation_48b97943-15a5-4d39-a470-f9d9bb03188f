import React from "react";
import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CartesianGrid,
    Tooltip,
    Area,
    AreaChart
} from "recharts";

export default function LineChartWrapper({data}) {
    return (
        <AreaChart
            width={600}
            height={350}
            data={data}
            syncId="anyId"
            margin={{
                top: 10,
                right: 30,
                left: 0,
                bottom: 0,
            }}
        >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Area type="monotone" dataKey="count" stroke="#2265ff" fill="#82ca9d" />
            {/* <Area type="monotone" dataKey="uv" stroke="#10bec5" fill="#d6e3ff" /> */}
        </AreaChart>
    );
}