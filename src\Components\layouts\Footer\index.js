import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Typography from '@material-ui/core/Typography';
import Grid from '@material-ui/core/Grid';
import moment from 'moment';
import { Container, Link } from '@material-ui/core';
import paymentLogo1 from 'Assets/payment/masterCardLogo.png';
import paymentLogo2 from 'Assets/payment/msCardImage.png';
import paymentLogo3 from 'Assets/payment/PayGate-Payment-Method-Logo-American-Express.png';
import paymentLogo5 from 'Assets/payment/VISA-Logo.png';
import footerBgImg from 'Assets/images/homePage/footerBgImg1.png';
import footerLogo from 'Assets/images/newLogo.svg';
import { Paths } from 'Routes/routePaths';
import { IsMobileDevice } from 'utils';

const useStyles = makeStyles(theme => ({
    root: {
        minHeight: 320,
        width: '100%',
        padding: '20px 0px',
        background: '#0881ca',
        backgroundImage: `url(${footerBgImg})`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover'
    },
    academyAccredWrapper: {
        maxWidth: 1040,
        position: 'relative',
        '& .accredSubTitle': {
            fontWeight: 'normal',
            fontSize: 15,
            color: '#fff',
            lineHeight: '20px',
            paddingBottom: 10
        }
    },
    footerBorder: {
        borderBottom: '1px solid #F2F2F2',
        width: '100%',
        marginTop: 20,
        marginBottom: 20,
    },
    paymentLogoWrap: {
        width: 80,
        padding: 5,
        marginRight: 15,
        borderRadius: 4,
        background: 'white',
        display: 'inline-block',
        marginBottom: 10
    },
    paymentLogo: {
        width: '100%',
        height: 60
    },
    secondaryTitle: {
        color: '#fff',
        fontWeight: 400,
        lineHeight: '20px'
    }
}));

function Footer({ t }) {
    const classes = useStyles();
    const isMobile = IsMobileDevice('1050');

    return (
        <div className={classes.root}>
            <Container className={classes.academyAccredWrapper}>
                <Grid container >
                    <Grid itme xs={12} >
                        <img src={footerLogo} alt='Footer Logo' />
                    </Grid>
                    <Grid itme xs={12} lg={6} sm={6} md={6} >
                        <Typography variant="h4" style={{ color: '#fff', fontWeight: 500, paddingBottom: 24, paddingTop: 10 }}>Contact</Typography>
                        <Typography className="accredSubTitle">
                            Email: <a style={{ color: 'white', textDecoration: 'none' }} href="mailto:<EMAIL>"><EMAIL></a>
                        </Typography>
                        <Typography className="accredSubTitle">
                            Telephone: <a style={{ color: 'white', textDecoration: 'none' }} href='tel:+27105958522'>+27 10 595 8522</a>
                        </Typography>
                        <Typography className="accredSubTitle">
                            Block C, Cedar Tree Medical and Office Park, Fourways, Sandton, Gauteng, 2055
                        </Typography>
                    </Grid>
                    <Grid itme xs={12} lg={6} sm={6} md={6}>
                        <div style={isMobile ? { float: 'right' } : { marginBottom: 20 }}>
                            <Typography variant="h4" style={{ color: '#fff', fontWeight: 500, paddingBottom: 24, paddingTop: 10 }}>Payment Methods</Typography>
                            {[paymentLogo1, paymentLogo2, paymentLogo3, paymentLogo5].map(img => (
                                <div style={(isMobile) ? null : { width: 70 }} className={classes.paymentLogoWrap}>
                                    <img alt='img1' src={img} className={classes.paymentLogo} />
                                </div>
                            ))}
                            <div style={{ width: '100%' }}>
                                <Link
                                    component="button"
                                    variant="body2"
                                    style={{ color: '#fff', paddingTop: 30 }}
                                    onClick={() => {
                                        window.open('/terms-and-conditions')
                                    }}
                                >
                                    Terms and Conditions
                                </Link>
                                <Link
                                    component="button"
                                    variant="body2"
                                    style={{ color: '#fff', paddingTop: 30, paddingLeft: 20 }}
                                    onClick={() => {
                                        window.open(Paths.PrivacyPolicy)
                                    }}
                                >
                                    Privacy Policy
                                </Link>
                            </div>
                        </div>
                    </Grid>
                </Grid>
            </Container>
            <div className={classes.footerBorder}></div>
            <Grid container>
                <Grid item xs={12} lg={12} sm={12} style={{ margin: 'auto' }}>
                    <Typography variant="body2" style={{ paddingBottom: 8, color: 'white', textAlign: 'center' }}>
                        © Copyright {moment().format('YYYY')}. All rights reserved
                    </Typography>
                </Grid>
            </Grid>
        </div>
    );
}

export default Footer;