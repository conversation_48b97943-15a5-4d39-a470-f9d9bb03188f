import React, { useEffect, useState } from 'react';
import Layout from "Components/layouts";
import { Button, CardActions, CircularProgress, Container, Grid, Typography, TextField } from '@material-ui/core';
import Card from '@material-ui/core/Card';
import { getCourseImage } from 'utils';
import { LazyLoadImage } from "react-lazy-load-image-component";
import { useHistory } from 'react-router-dom';

export function getDescription(desc, maxLength = null) {
    if (!desc) return '';
    let descArr = desc.split('#');
    descArr = descArr.filter(itm => itm !== '');
    if (descArr.length > 1) {
        return (maxLength ? descArr[0].length > maxLength : descArr[0].length > 100) ? `${descArr[0].slice(0, maxLength ? maxLength : 230)}...` : descArr[0];
    }
    return desc;
}

export const courseTypes = [
    {
        name: "Short Course",
        id: "Short Course"
    },
    {
        name: "Single Course",
        id: 'Single Course'
    },
    {
        name: "Learning Path",
        id: 'Learning Path'
    }
];

const CourseLearning = () => {
    const [courses, setCourses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [page, setPage] = useState(1);
    const [dropdowns, setDropdowns] = useState({
        skill_levels: [],
        course_types: [],
        plans: [],
        roles: [],
        domains: [],
    });
    const [filters, setFilters] = useState({
        search: '',
        skill_levels: '',
        course_types: '',
        domains: '',
        roles: '',
    });
    const PAGE_SIZE = 12; // Changed from 9 to 12
    const [totalPages, setTotalPages] = useState(1);
    const [totalCourses, setTotalCourses] = useState(0);
    
    // Customer ID - should be dynamic based on logged-in user
    const CUSTOMER_ID = localStorage.getItem('uuid') || '26469e57-9cfa-42dc-8ea3-32d80ecb0c74';

    const handleDomainCheckbox = (domain) => {
        let selected = filters.domains ? filters.domains.split(',') : [];
        if (selected.includes(domain)) {
            selected = selected.filter(d => d !== domain);
        } else {
            selected.push(domain);
        }
        setFilters(prev => ({ ...prev, domains: selected.join(',') }));
        setPage(1);
    };

    const renderPagination = () => {
        if (totalPages <= 1) return null;
        const pages = [];
        let start = Math.max(1, page - 2);
        let end = Math.min(totalPages, page + 2);
        if (end - start < 4) {
            if (start === 1) end = Math.min(totalPages, start + 4);
            if (end === totalPages) start = Math.max(1, end - 4);
        }
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', margin: '32px 0', gap: 8 }}>
                <Button 
                    variant="outlined" 
                    onClick={() => setPage(page - 1)} 
                    disabled={page === 1}
                    style={{ minWidth: 80 }}
                >
                    Previous
                </Button>
                {start > 1 && (
                    <>
                        <Button variant="text" onClick={() => setPage(1)}>1</Button>
                        {start > 2 && <span style={{ margin: '0 8px' }}>...</span>}
                    </>
                )}
                {pages.map(p => (
                    <Button
                        key={p}
                        variant={p === page ? "contained" : "outlined"}
                        color={p === page ? "primary" : "default"}
                        onClick={() => setPage(p)}
                        style={{ minWidth: 40 }}
                    >
                        {p}
                    </Button>
                ))}
                {end < totalPages - 1 && <span style={{ margin: '0 8px' }}>...</span>}
                {end < totalPages && (
                    <Button variant="text" onClick={() => setPage(totalPages)}>{totalPages}</Button>
                )}
                <Button 
                    variant="outlined" 
                    onClick={() => setPage(page + 1)} 
                    disabled={page === totalPages}
                    style={{ minWidth: 80 }}
                >
                    Next
                </Button>
            </div>
        );
    };

    // Fetch courses with filters and pagination using fetch WITHOUT token
    useEffect(() => {
        const fetchCourses = async () => {
            if (!CUSTOMER_ID) {
                setError('User ID not found');
                setLoading(false);
                return;
            }

            setLoading(true);
            setError(null);
            
            try {
                // Build URL with query parameters
                const url = new URL(`https://api-staging.deviare.africa/main/CustomerCourses/${CUSTOMER_ID}`);
                
                url.searchParams.append('page', page.toString());
                url.searchParams.append('page_size', PAGE_SIZE.toString()); // Add page_size parameter

                // Add filters to params only if they have values
                if (filters.search?.trim()) {
                    url.searchParams.append('search', filters.search.trim());
                }
                if (filters.skill_levels) {
                    url.searchParams.append('skill_levels', filters.skill_levels);
                }
                if (filters.course_types) {
                    url.searchParams.append('course_types', filters.course_types);
                }
                if (filters.domains) {
                    url.searchParams.append('domains', filters.domains);
                }
                if (filters.roles) {
                    url.searchParams.append('roles', filters.roles);
                }

                console.log('Fetching courses from URL:', url.toString());

                const response = await fetch(url.toString(), {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    mode: 'cors',
                    credentials: 'omit',
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('API Response:', data);
                
                if (data.success && data.data) {
                    // Set courses data
                    setCourses(data.data.courses || []);
                    setTotalPages(data.data.pagination?.total_pages || 1);
                    setTotalCourses(data.data.pagination?.total_courses || data.data.pagination?.total_items || 0);
                    
                    // Set dropdowns data from the same API response
                    if (data.data.dropdowns) {
                        console.log('Setting dropdowns from CustomerCourses API:', data.data.dropdowns);
                        setDropdowns({
                            skill_levels: data.data.dropdowns.skill_levels || [],
                            course_types: data.data.dropdowns.course_types || [],
                            plans: data.data.dropdowns.plans || [],
                            roles: data.data.dropdowns.roles || [],
                            domains: data.data.dropdowns.domains || [],
                        });
                    }
                } else {
                    setCourses([]);
                    setTotalPages(1);
                    setTotalCourses(0);
                    
                    if (data.message) {
                        console.warn('API message:', data.message);
                    }
                }
            } catch (err) {
                console.error('Error fetching courses:', err);
                
                let errorMessage = 'Failed to fetch courses';
                
                if (err.name === 'TypeError' && err.message.includes('fetch')) {
                    errorMessage = 'Network error - please check your connection';
                } else if (err.message.includes('HTTP error')) {
                    errorMessage = `Server error: ${err.message}`;
                } else {
                    errorMessage = err.message || 'Failed to fetch courses';
                }
                
                setError(errorMessage);
                setCourses([]);
                setTotalPages(1);
                setTotalCourses(0);
            } finally {
                setLoading(false);
            }
        };

        fetchCourses();
    }, [page, filters, CUSTOMER_ID]);

    const handleFilterChange = (filterKey, value) => {
        console.log(`Filter changed: ${filterKey} = ${value}`);
        setFilters(prev => ({ ...prev, [filterKey]: value }));
        setPage(1); // Reset to first page when filter changes
    };

    const handleClearFilters = () => {
        setFilters({
            search: '',
            skill_levels: '',
            course_types: '',
            domains: '',
            roles: '',
        });
        setPage(1);
    };

    const handleSearchChange = (event) => {
        const value = event.target.value;
        setFilters(prev => ({ ...prev, search: value }));
        // Debounce search to avoid too many API calls
        if (value.trim() === '' || value.length >= 3) {
            setPage(1);
        }
    };

    const handleRetryFetch = () => {
        setError(null);
        setPage(1);
        // This will trigger the useEffect to refetch
    };

    const filterOptions = [
        { label: 'Skill Level', key: 'skill_levels', options: dropdowns.skill_levels || [] },
        { label: 'Course Type', key: 'course_types', options: dropdowns.course_types || [] },
        { label: 'Associated Role', key: 'roles', options: dropdowns.roles || [] },
    ];

    return (
        <main style={{ background: '#fff', minHeight: '100vh' }}>
            <Container maxWidth="lg" style={{ paddingTop: 32 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
                    <Typography variant="h4" style={{ fontWeight: 700, color: "black" }}>
                        My Courses
                    </Typography>
                    <Typography variant="body1" style={{ color: '#666' }}>
                        {totalCourses} courses found
                    </Typography>
                </div>
                
                <div style={{ marginBottom: 24 }}>
                    {/* Search Bar */}
                    <Grid container spacing={2} style={{ marginBottom: 16 }}>
                        <Grid item xs={12} md={4}>
                            <TextField
                                fullWidth
                                variant="outlined"
                                placeholder="Search courses..."
                                value={filters.search}
                                onChange={handleSearchChange}
                                size="small"
                                style={{ backgroundColor: '#fff' }}
                            />
                        </Grid>
                    </Grid>

                    {/* Filter Dropdowns */}
                    <Grid container spacing={2} style={{ marginBottom: 24 }}>
                        {filterOptions.map((filter) => (
                            <Grid item key={filter.key} xs={12} sm={6} md={3}>
                                <div style={{ position: 'relative', width: '100%' }}>
                                    <select
                                        value={filters[filter.key]}
                                        onChange={e => handleFilterChange(filter.key, e.target.value)}
                                        style={{
                                            width: '100%',
                                            padding: '10px 16px',
                                            borderRadius: 8,
                                            border: '1px solid #e0e0e0',
                                            background: '#fff',
                                            fontSize: 15
                                        }}
                                    >
                                        <option value="">{filter.label}</option>
                                        {filter.options.map(opt => (
                                            <option key={opt} value={opt}>{opt}</option>
                                        ))}
                                    </select>
                                    {filters[filter.key] && (
                                        <Button
                                            style={{
                                                position: 'absolute',
                                                top: 4,
                                                right: 8,
                                                minWidth: 24,
                                                height: 24,
                                                padding: 0,
                                                color: '#888',
                                                background: 'transparent',
                                                zIndex: 2,
                                                lineHeight: 1,
                                                fontSize: 18,
                                                borderRadius: '50%'
                                            }}
                                            onClick={() => handleFilterChange(filter.key, '')}
                                        >
                                            ×
                                        </Button>
                                    )}
                                </div>
                            </Grid>
                        ))}
                        <Grid item xs={12} sm={6} md={3}>
                            <Button 
                                variant="outlined" 
                                onClick={handleClearFilters} 
                                style={{ 
                                    width: '100%', 
                                    height: 42,
                                    borderColor: '#e0e0e0',
                                    color: '#666'
                                }}
                            >
                                Clear All
                            </Button>
                        </Grid>
                    </Grid>

                    <div style={{ display: 'flex', flexDirection: 'row', gap: 32 }}>
                        {/* Domain filter as checkboxes on the left */}
                        <div style={{ minWidth: 220 }}>
                            <Typography variant="subtitle1" style={{ fontWeight: 700, marginBottom: 8, color: "#70a1cf" }}>
                                Domain
                            </Typography>
                            <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                                {(dropdowns.domains || []).map(domain => {
                                    const selectedDomains = filters.domains ? filters.domains.split(',') : [];
                                    return (
                                        <label key={domain} style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', marginBottom: 4 }}>
                                            <input
                                                type="checkbox"
                                                checked={selectedDomains.includes(domain)}
                                                onChange={() => handleDomainCheckbox(domain)}
                                                style={{ marginRight: 8 }}
                                            />
                                            <span style={{ fontSize: 14 }}>{domain}</span>
                                        </label>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Course cards on the right */}
                        <div style={{ flex: 1 }}>
                            {loading ? (
                                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
                                    <CircularProgress size={50} />
                                    <Typography style={{ marginLeft: 16, color: '#666' }}>
                                        Loading courses...
                                    </Typography>
                                </div>
                            ) : error ? (
                                <div style={{ textAlign: 'center', marginTop: 40, padding: 40 }}>
                                    <Typography color="error" variant="h6" style={{ marginBottom: 16 }}>
                                        {error}
                                    </Typography>
                                    <Button 
                                        variant="contained" 
                                        color="primary" 
                                        onClick={handleRetryFetch}
                                        style={{ marginTop: 16 }}
                                    >
                                        Retry
                                    </Button>
                                </div>
                            ) : (
                                <>
                                    {courses.length === 0 ? (
                                        <div style={{ textAlign: 'center', marginTop: 40, padding: 40 }}>
                                            <Typography variant="h6" style={{ color: '#666', marginBottom: 16 }}>
                                                No courses found
                                            </Typography>
                                            <Typography variant="body2" style={{ color: '#999' }}>
                                                Try adjusting your filters or search terms
                                            </Typography>
                                        </div>
                                    ) : (
                                        <Grid container spacing={3}> {/* Reduced spacing from 4 to 3 for better fit */}
                                            {courses.map(course => (
                                                <Grid item xs={12} sm={6} md={3} key={course.uuid || course.id || course.name} style={{ display: 'flex', justifyContent: 'center' }}>
                                                    {/* Changed md={4} to md={3} to show 4 cards per row (12/3=4) */}
                                                    <Card style={{
                                                        borderRadius: 16,
                                                        boxShadow: '0 2px 12px #e0e7ef',
                                                        minHeight: 520,
                                                        maxHeight: 520,
                                                        maxWidth: 300, // Reduced from 340 to 300 for better fit
                                                        width: '100%',
                                                        display: 'flex',
                                                        flexDirection: 'column',
                                                        justifyContent: 'space-between',
                                                        alignItems: 'stretch',
                                                        margin: '0 auto',
                                                    }}>
                                                        <div style={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                                                            <div style={{ background: '#aeb0be', height: 160, borderRadius: '16px 16px 0 0', marginBottom: 12, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                                                <LazyLoadImage 
                                                                    src={course.thumbnail_url || course.imageUrl || getCourseImage(course)} 
                                                                    alt="Course" 
                                                                    style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: '16px 16px 0 0' }} 
                                                                />
                                                            </div>
                                                            <div style={{ padding: '0 20px', flex: 1, display: 'flex', flexDirection: 'column' }}>
                                                                <Typography variant="subtitle1" style={{ fontWeight: 700, marginBottom: 4 }}>
                                                                    {course.name}
                                                                </Typography>
                                                                <Typography variant="body2" style={{ color: '#6b6b6b', marginBottom: 8, maxHeight: 60, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                                                                    {getDescription(course?.description || course?.course_overview, 80)}
                                                                </Typography>
                                                                {((course?.description?.length > 80) || (course?.course_overview?.length > 80)) && (
                                                                    <Typography
                                                                        variant="body2"
                                                                        style={{ color: '#1976d2', cursor: 'pointer', marginBottom: 8 }}
                                                                        onClick={() => window.location.href = `/course/${course.uuid}?course_id=${course.uuid}`}
                                                                    >
                                                                        Learn More
                                                                    </Typography>
                                                                )}
                                                                <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                                                                    <Typography variant="caption" style={{ fontWeight: 600, marginRight: 8 }}>
                                                                        {course.duration || '6 months'}
                                                                    </Typography>
                                                                    <span style={{ fontSize: 12, color: '#b0b0b0', marginRight: 8 }}>&#9679;</span>
                                                                    <Typography variant="caption" style={{ color: '#4caf50', fontWeight: 600 }}>
                                                                        {course.rating || '4.9/5'} <span style={{ color: '#ffc107' }}>★</span>
                                                                    </Typography>
                                                                    <span style={{ fontSize: 12, color: '#b0b0b0', margin: '0 8px' }}>&#9679;</span>
                                                                    <Typography variant="caption" style={{ color: '#6b6b6b' }}>
                                                                        {course.level || course.skill_level || 'Beginner'}
                                                                    </Typography>
                                                                </div>
                                                                <Typography variant="caption" style={{ color: '#b0b0b0', marginBottom: 12 }}>
                                                                    {course.package || 'Professional Certificate'}
                                                                </Typography>
                                                            </div>
                                                        </div>
                                                        <CardActions style={{ padding: '24px 20px', justifyContent: 'flex-end' }}>
                                                            <Button
                                                                variant="contained"
                                                                color="primary"
                                                                style={{ borderRadius: 8, minWidth: 120, fontWeight: 600, fontSize: 16, padding: '12px 0', display: 'block' }}
                                                                onClick={() => window.location.href = `/course/detail/${course.uuid}?course_id=${course.uuid}`}
                                                            >
                                                                {course?.is_purchased ? 'Start Learning' : 'View'}
                                                            </Button>
                                                        </CardActions>
                                                    </Card>
                                                </Grid>
                                            ))}
                                        </Grid>
                                    )}
                                    {renderPagination()}
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </Container>
        </main>
    );
}

export default CourseLearning;
