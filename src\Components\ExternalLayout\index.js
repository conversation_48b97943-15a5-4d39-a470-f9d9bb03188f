import React from 'react';
import Container from '@material-ui/core/Container';
import { makeStyles } from '@material-ui/core/styles';
import variables from "../../Sys/variable.scss";
import ExHeader from './ExHeader';
import LoginDialogBox from '../../Pages/LoginDialogBox';
import SignUpDialogBox from '../../Pages/SignUpDialogBox';

const useStyles = makeStyles(() => ({
    content: {
        width: '100%',
        padding: '0px',
        backgroundColor: variables.bodyColor
    },
    mainSec: {
        display: 'flex'
    }
}));

const ExtarnalLayout = ({ openModalAction, handleEmptyModalAction, children }) => {

    const classes = useStyles();
    const [loginOpen, setLoginOpen] = React.useState(false);
    const [signUpOpen, setSignUpOpen] = React.useState(false);

    React.useEffect(() => {
        if (openModalAction === 'login') {
            setLoginOpen(true);
        }
        if (openModalAction === 'signup') {
            setSignUpOpen(true);
        }
    }, [openModalAction]);

    return (
        <>
            <ExHeader handleOpenModal={(type) => {
                if (type === 'login') {
                    setLoginOpen(true);
                }
                if (type === 'signup') {
                    setSignUpOpen(true);
                }
            }} />
            <div className={classes.mainSec}>
                <Container maxWidth="xlg" className={classes.content} >
                    {children}
                </Container>
                <LoginDialogBox
                    open={loginOpen}
                    handleOpenSignUp={() => {
                        setSignUpOpen(true);
                        setLoginOpen(false);
                    }}
                    handleClose={() => {
                        setLoginOpen(false);
                        handleEmptyModalAction();
                    }}
                />
                <SignUpDialogBox
                    open={signUpOpen}
                    handleOpenLogin={() => {
                        setSignUpOpen(false);
                        setLoginOpen(true);
                    }}
                    handleClose={() => {
                        setSignUpOpen(false);
                        handleEmptyModalAction();
                    }}
                />
            </div>
        </>
    );
}

export default ExtarnalLayout;