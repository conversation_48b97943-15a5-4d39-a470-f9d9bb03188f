import React, { useEffect, useState } from 'react';
import Layout from "Components/layouts";
import { Button, CardActions, CircularProgress, Container, Grid, Typography } from '@material-ui/core';
import Card from '@material-ui/core/Card';
import { getCourseImage } from 'utils';
import { LazyLoadImage } from "react-lazy-load-image-component";
import { useHistory } from 'react-router-dom';
import CloseIcon from '@material-ui/icons/Close';

export function getDescription(desc, maxLength = null) {
    let descArr = desc.split('#');
    descArr = descArr.filter(itm => itm !== '');
    if (descArr.length > 1) {
        return (maxLength ? descArr[0].length > maxLength : descArr[0].length > 100) ? `${descArr[0].slice(0, maxLength ? maxLength : 230)}...` : descArr[0];
    }
    return desc;
}

export const courseTypes = [
    {
        name: "Short Course",
        id: "Short Course"
    },
    {
        name: "Single Course",
        id: 'Single Course'
    },
    {
        name: "Learning Path",
        id: 'Learning Path'
    }
];

export const CourseItem = ({ course, handleBuyNowClick }) => {
    const [showFull, setShowFull] = useState(false);
    const history = useHistory();
    const courseImg = course.imageUrl || getCourseImage(course);
    const overview = course?.course_overview || getDescription(course.description);
    const isLong = overview && overview.length > 50;
    const displayText = showFull ? overview : (isLong ? `${overview.slice(0, 50)}...` : overview);

    const handleLearnMore = (e) => {
        e.stopPropagation();
        setShowFull(true);
    };

    const handleEnroll = () => {
        history.push(`/course/${course.uuid}`);
    };

    return (
        <Grid item xs={12} sm={6} md={3}>
            <Card style={{ borderRadius: 16, boxShadow: '0 2px 12px #e0e7ef', minHeight: 380, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                <div>
                    <div style={{ background: '#aeb0be', height: 160, borderRadius: '16px 16px 0 0', marginBottom: 12, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <LazyLoadImage src={courseImg} alt="Course" style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: '16px 16px 0 0' }} />
                    </div>
                    <div style={{ padding: '0 20px' }}>
                        <Typography variant="subtitle1" style={{ fontWeight: 700, marginBottom: 4 }}>{course.name}</Typography>
                        <Typography variant="body2" style={{ color: '#6b6b6b', marginBottom: 8 }}>
                            {displayText}
                            {isLong && !showFull && (
                                <span style={{ color: '#1976d2', cursor: 'pointer', marginLeft: 4 }} onClick={handleLearnMore}>Learn More</span>
                            )}
                        </Typography>
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                            <Typography variant="caption" style={{ fontWeight: 600, marginRight: 8 }}>{course.duration || '6 months'}</Typography>
                            <span style={{ fontSize: 12, color: '#b0b0b0', marginRight: 8 }}>&#9679;</span>
                            <Typography variant="caption" style={{ color: '#4caf50', fontWeight: 600 }}>{course.rating || '4.9/5'} <span style={{ color: '#ffc107' }}>★</span></Typography>
                            <span style={{ fontSize: 12, color: '#b0b0b0', margin: '0 8px' }}>&#9679;</span>
                            <Typography variant="caption" style={{ color: '#6b6b6b' }}>{course.skill_level || 'Beginner'}</Typography>
                        </div>
                        <Typography variant="caption" style={{ color: '#b0b0b0' }}>Professional Certificate</Typography>
                    </div>
                </div>
                <CardActions style={{ padding: '16px 20px', justifyContent: 'flex-end' }}>
                    <Button
                        variant="contained"
                        color="primary"
                        style={{ borderRadius: 8, minWidth: 100, fontWeight: 600 }}
                        onClick={() => {
                            window.location.href = `/course/${course.uuid}`;
                        }}
                    >
                        {course?.is_purchased ? 'Start Learning' : 'View'}
                    </Button>
                </CardActions>
            </Card>
        </Grid>
    );
}

const CourseListing = () => {
    const [courses, setCourses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [page, setPage] = useState(1);
    const [dropdowns, setDropdowns] = useState({
        skill_levels: [],
        course_types: [],
        plans: [],
        roles: [],
        domains: [],
    });
    const [filters, setFilters] = useState({
        skill_levels: '',
        course_types: '',
        plans: '',
        roles: '',
        domains: '',
    });
    const PAGE_SIZE = 9;
    const [totalPages, setTotalPages] = useState(1);

    const getFilterKey = idx => Object.keys(filters)[idx];

    const handleDomainCheckbox = (domain) => {
        let selected = filters.domains ? filters.domains.split(',') : [];
        if (selected.includes(domain)) {
            selected = selected.filter(d => d !== domain);
        } else {
            selected.push(domain);
        }
        setFilters(prev => ({ ...prev, domains: selected.join(',') }));
        setPage(1);
    };

    const renderPagination = () => {
        if (totalPages <= 1) return null;
        const pages = [];
        let start = Math.max(1, page - 2);
        let end = Math.min(totalPages, page + 2);
        if (end - start < 4) {
            if (start === 1) end = Math.min(totalPages, start + 4);
            if (end === totalPages) start = Math.max(1, end - 4);
        }
        for (let i = start; i <= end; i++) {
            pages.push(i);
        }
        return (
            <div style={{ display: 'flex', justifyContent: 'center', margin: '32px 0', gap: 8 }}>
                <Button variant="outlined" onClick={() => setPage(page - 1)} disabled={page === 1}>Previous</Button>
                {start > 1 && <Button variant="text" onClick={() => setPage(1)}>1</Button>}
                {start > 2 && <span>...</span>}
                {pages.map(p => (
                    <Button
                        key={p}
                        variant={p === page ? "contained" : "outlined"}
                        color={p === page ? "primary" : "default"}
                        onClick={() => setPage(p)}
                    >
                        {p}
                    </Button>
                ))}
                {end < totalPages - 1 && <span>...</span>}
                {end < totalPages && <Button variant="text" onClick={() => setPage(totalPages)}>{totalPages}</Button>}
                <Button variant="outlined" onClick={() => setPage(page + 1)} disabled={page === totalPages}>Next</Button>
            </div>
        );
    };

    useEffect(() => {
        setLoading(true);
        // Build query params for filters
        const params = new URLSearchParams({
            page,
            page_size: PAGE_SIZE,
            skill_levels: filters.skill_levels || '',
            course_types: filters.course_types || '',
            plans: filters.plans || '',
            roles: filters.roles || '',
            domains: filters.domains || '',
        });
        fetch(`https://api-staging.deviare.africa/main/coursedropdownsandlist?${params.toString()}`)
            .then(res => res.json())
            .then(data => {
                if (data.status && data.data) {
                    setDropdowns(data.data.dropdowns || {});
                    setCourses(data.data.courses || []);
                    setTotalPages(data.data.pagination?.total_pages || 1);
                } else {
                    setCourses([]);
                }
                setLoading(false);
            })
            .catch(err => {
                setError('Failed to fetch courses');
                setLoading(false);
            });
    }, [page, filters]);

    const handleFilterChange = (filterKey, value) => {
        setFilters(prev => ({ ...prev, [filterKey]: value }));
        setPage(1);
    };

    const handleClearFilters = () => {
        setFilters({
            skill_levels: '',
            course_types: '',
            plans: '',
            roles: '',
            domains: '',
        });
        setPage(1);
    };

    const filterOptions = [
        { label: 'Skill Level', options: dropdowns.skill_levels || [] },
        { label: 'Course Type', options: dropdowns.course_types || [] },
        { label: 'Plan', options: dropdowns.plans || [] },
        { label: 'Associated Role', options: dropdowns.roles || [] },
    ];

    return (
        <Layout sidebarView={false}>
            <main style={{ background: '#fff', minHeight: '100vh' }}>
                <Container maxWidth="lg" style={{ paddingTop: 32 }}>
                    <Typography variant="h4" style={{ fontWeight: 700, marginBottom: 16 ,color:"black"}}>Courses</Typography>
                    <div style={{ marginBottom: 24 }}>
                        <Grid container spacing={2} style={{ marginBottom: 24 }}>
                            {filterOptions.map((filter, idx) => {
                                const key = getFilterKey(idx);
                                return (
                                    <Grid item key={filter.label} xs={12} sm={6} md={2} style={{ position: 'relative' }}>
                                        <div style={{ position: 'relative', width: '100%' }}>
                                            <select
                                                value={filters[key]}
                                                onChange={e => handleFilterChange(key, e.target.value)}
                                                style={{
                                                    width: '100%',
                                                    padding: '10px 16px',
                                                    borderRadius: 8,
                                                    border: '1px solid #e0e0e0',
                                                    background: '#fff',
                                                    fontSize: 15
                                                }}
                                            >
                                                <option value="">{filter.label}</option>
                                                {filter.options.map(opt => (
                                                    <option key={opt} value={opt}>{opt}</option>
                                                ))}
                                            </select>
                                            {filters[key] && (
                                                <Button
                                                    style={{
                                                        position: 'absolute',
                                                        top: 4,
                                                        right: 8,
                                                        minWidth: 24,
                                                        height: 24,
                                                        padding: 0,
                                                        color: '#888',
                                                        background: 'transparent',
                                                        zIndex: 2,
                                                        lineHeight: 1,
                                                        fontSize: 18,
                                                        borderRadius: '50%'
                                                    }}
                                                    onClick={() => handleFilterChange(key, '')}
                                                    tabIndex={-1}
                                                >
                                                    ×
                                                </Button>
                                            )}
                                        </div>
                                    </Grid>
                                );
                            })}
                            <Grid item xs={12} sm={6} md={2}>
                                <Button variant="outlined" color="white" onClick={handleClearFilters} style={{ width: '100%', height: '100%' }}>
                                    Clear All
                                </Button>
                            </Grid>
                        </Grid>
                        <div style={{ display: 'flex', flexDirection: 'row', gap: 32 }}>
                            {/* Domain filter as checkboxes on the left */}
                            <div style={{ minWidth: 220 }}>
                                <Typography variant="subtitle1" style={{ fontWeight: 700, marginBottom: 8, color: "#70a1cf" }}>Domain</Typography>
                                <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                                    {(dropdowns.domains || []).map(domain => {
                                        const selectedDomains = filters.domains ? filters.domains.split(',') : [];
                                        return (
                                            <label key={domain} style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', marginBottom: 4 }}>
                                                <input
                                                    type="checkbox"
                                                    checked={selectedDomains.includes(domain)}
                                                    onChange={() => handleDomainCheckbox(domain)}
                                                    style={{ marginRight: 8 }}
                                                />
                                                <span>{domain}</span>
                                            </label>
                                        );
                                    })}
                                </div>
                            </div>
                            {/* Course cards on the right */}
                            <div style={{ flex: 1 }}>
                                {loading ? (
                                    <CircularProgress style={{ display: 'block', margin: '0 auto' }} />
                                ) : error ? (
                                    <Typography color="error">{error}</Typography>
                                ) : (
                                    <>
                                        <Grid container spacing={4}>
                                            {courses.length === 0 ? (
                                                <Typography>No courses found</Typography>
                                            ) : (
                                                courses.map(course => (
                                                    <Grid item xs={12} sm={6} md={4} key={course.uuid || course.name} style={{ display: 'flex', justifyContent: 'center' }}>
                                                        <Card style={{
                                                            borderRadius: 16,
                                                            boxShadow: '0 2px 12px #e0e7ef',
                                                            minHeight: 520,
                                                            maxHeight: 520,
                                                            maxWidth: 340,
                                                            width: '100%',
                                                            display: 'flex',
                                                            flexDirection: 'column',
                                                            justifyContent: 'space-between',
                                                            alignItems: 'stretch',
                                                            margin: '0 auto',
                                                        }}>
                                                            <div style={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                                                                <div style={{ background: '#aeb0be', height: 160, borderRadius: '16px 16px 0 0', marginBottom: 12, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                                                    <LazyLoadImage src={course.thumbnail_url || course.imageUrl || getCourseImage(course)} alt="Course" style={{ width: '100%', height: '100%', objectFit: 'cover', borderRadius: '16px 16px 0 0' }} />
                                                                </div>
                                                                <div style={{ padding: '0 20px', flex: 1, display: 'flex', flexDirection: 'column' }}>
                                                                    <Typography variant="subtitle1" style={{ fontWeight: 700, marginBottom: 4 }}>{course.name}</Typography>
                                                                    <Typography variant="body2" style={{ color: '#6b6b6b', marginBottom: 8, maxHeight: 60, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                                                                        {getDescription(course?.description, 80)}
                                                                    </Typography>
                                                                    {(course?.description?.length > 80) && (
                                                                        <Typography
                                                                            variant="body2"
                                                                            style={{ color: '#1976d2', cursor: 'pointer', marginBottom: 8 }}
                                                                            onClick={() => window.location.href = `/course/${course.uuid}?course_id=${course.uuid}`}
                                                                        >
                                                                            Learn More
                                                                        </Typography>
                                                                    )}
                                                                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                                                                        <Typography variant="caption" style={{ fontWeight: 600, marginRight: 8 }}>
                                                                            {course.duration || '6 months'}
                                                                        </Typography>
                                                                        <span style={{ fontSize: 12, color: '#b0b0b0', marginRight: 8 }}>&#9679;</span>
                                                                        <Typography variant="caption" style={{ color: '#4caf50', fontWeight: 600 }}>
                                                                            {course.rating || '4.9/5'} <span style={{ color: '#ffc107' }}>★</span>
                                                                        </Typography>
                                                                        <span style={{ fontSize: 12, color: '#b0b0b0', margin: '0 8px' }}>&#9679;</span>
                                                                        <Typography variant="caption" style={{ color: '#6b6b6b' }}>
                                                                            {course.level || course.skill_level || 'Beginner'}
                                                                        </Typography>
                                                                    </div>
                                                                    <Typography variant="caption" style={{ color: '#b0b0b0', marginBottom: 12 }}>
                                                                        {course.certification || 'Professional Certificate'}
                                                                    </Typography>
                                                                </div>
                                                            </div>
                                                            <CardActions style={{ padding: '24px 20px', justifyContent: 'flex-end' }}>
                                                                <Button
                                                                    variant="contained"
                                                                    color="primary"
                                                                    style={{ borderRadius: 8, minWidth: 120, fontWeight: 600, fontSize: 18, padding: '12px 0', display: 'block' }}
                                                                    onClick={() => window.location.href = `/course/${course.uuid}?course_id=${course.uuid}`}
                                                                >
                                                                    {course?.is_purchased ? 'Start Learning' : 'View'}
                                                                </Button>
                                                            </CardActions>
                                                        </Card>
                                                    </Grid>
                                                ))
                                            )}
                                        </Grid>
                                        {renderPagination()}
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </Container>
            </main>
        </Layout>
    );
}

export default CourseListing;