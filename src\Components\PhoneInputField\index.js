import React from 'react';
import 'react-phone-number-input/style.css';
import PhoneInput from 'react-phone-number-input';

const PhoneInputField = ({ value, handleChange, name, disabled, customClass }) => {
    return (
        <PhoneInput
            disabled={disabled || false}
            international
            defaultCountry="ZA"
            className={customClass ? customClass : `phone-input-style ${disabled ? 'input-disabled' : ''}`}
            country={'za'}
            value={value}
            onChange={handleChange}
            // inputProps={{
            //     name: name || 'phone',
            //     required: true
            // }}
        />
    )
}

export default PhoneInputField;