import React from 'react';
import { LoadScript, StandaloneSearchBox } from '@react-google-maps/api';
import { Input } from '@material-ui/core';


const AutocompleteAddress = ({
    handlePlaceChanged,
    autocompleteRef,
    customStyle,
    value
}) => {

    return (
        <LoadScript googleMapsApiKey={process.env.REACT_APP_GOOGLE_MAP_API_KEY} libraries={["places"]}>
            <StandaloneSearchBox
                onPlacesChanged={handlePlaceChanged}
                onLoad = {ref => autocompleteRef.current = ref}>
                    <Input 
                        disableUnderline 
                        type={'text'}
                        style={{
                            borderColor: '#bebaba',
                            minWidth: 180,
                            width: '100%',
                            borderRadius: '25px',
                            ...(customStyle && customStyle)
                        }}
                        placeholder={"Enter Location"}
                        color={'secondary'}
                    />
            </StandaloneSearchBox>
        </LoadScript> 
    );
}

export default AutocompleteAddress;