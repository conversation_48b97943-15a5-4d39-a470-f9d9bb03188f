import axios from 'axios';
import AxiosInterCeptors from './AxiosInterCeptors';

export class Dashboard extends AxiosInterCeptors {
    
    getDetailDashboardCustomer() {
        return axios.get('main/customeradmindashboard')
            .then(res => res.data);
    }
    getDetailDashboardSuperAdmin() {
        return axios.get('main/superadmindashboard')
            .then(res => res.data);
    }
    getDetailDashboardUser() {
        return axios.get('main/userdashboard')
            .then(res => res.data);
    }
    getPopularCourseGraph(query) {
      return axios.get('main/popularcoursegraph?' + query)
          .then(res => res.data);
  }
}
