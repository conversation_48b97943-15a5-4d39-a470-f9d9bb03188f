import * as React from 'react';
import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';
import { Container, Table, TableCell, TableRow, TableHead, Typography, TableContainer, TableBody, Button } from '@material-ui/core';
import Layout from 'Components/layouts';
import OfficeImg from '../../Assets/images/homePage/accreditionBanner.png';
import { ContactUsPanel, accreditationCourses, initContactFields } from 'Pages/MainPage';
import CustomTabs from 'Components/CustomTabs';
import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';
import { IsMobileDevice, notify } from 'utils';
import { REQUIRED_ERROR } from 'utils/constants';
import { CustomerServices } from 'Services/CustomerServices';
import MICT_SETA from 'Assets/images/homePage/Mict.png';
import IITPSA from 'Assets/brandLogos/IITPSA.png';
import UNISA from 'Assets/brandLogos/UNISA.jpeg';
import { Paths } from 'Routes/routePaths';
import { useHistory } from 'react-router-dom/cjs/react-router-dom';
import PricingPlans from 'Pages/MainPage/PricingPlans';

const useStyles = makeStyles((theme) => ({
    cardRoot: {
        background: '#F9FAFB',
        width: '100%',
        marginBottom: 10,
        minHeight: 170
    },
    bannerWrap: {
        paddingTop: 30,
        minHeight: 220,
        paddingBottom: 40,
        backgroundImage: `url(${OfficeImg})`,
        backgroundPosition: '0px 0px, 50% 50%',
        backgroundSize: 'auto, cover',
        backgroundRepeat: 'repeat, no-repeat',
        '& .accredTitle': {
            color: '#fff',
            fontWeight: 700,
            fontSize: 32,
            lineHeight: '44px',
            paddingBottom: 10
        },
        '& .accredSubTitle': {
            fontWeight: 'normal',
            color: '#fff',
            fontSize: 15,
            lineHeight: '20px',
            paddingBottom: 10
        }
    },
    innerAcredWrap: {
        position: 'relative',
        '& .accredTitle': {
            fontWeight: 700,
            fontSize: 32,
            lineHeight: '44px',
            paddingBottom: 10
        },
        '& .accredSubTitle': {
            fontWeight: 'normal',
            fontSize: 15,
            lineHeight: '20px',
            paddingBottom: 10
        },
        '& .accredCourseTitle': {
            fontWeight: 500,
            fontSize: 20,
            color: '#0080CA',
            lineHeight: '24px',
            paddingBottom: 10
        },
        '& .accredCourseSubTitle': {
            fontWeight: 'normal',
            fontSize: 15,
            lineHeight: '20px',
            paddingBottom: 10
        },
        '& .heading': {
            color: "#fff",
            marginTop: 15,
            marginBottom: 10,
            backgroundColor: '#0080CA',
            padding: 12,
            borderRadius: "8px",
            display: 'flex',
            justifyContent: 'space-between'
        },
        '& .accredCardItem': {
            background: '#F3F9FF',
            borderRadius: 24,
            padding: 8,
            textAlign: 'center',
            '& p': {
                fontWeight: 'normal',
                fontSize: 13,
                lineHeight: '20px',
                color: '#0080CA'
            }
        },
        '& .accredCardItemMob': {
            background: '#F3F9FF',
            borderRadius: 24,
            padding: '0px 8px',
            '& p': {
                fontWeight: 'normal',
                fontSize: 13,
                lineHeight: '20px',
                color: '#0080CA'
            }
        }
    },
    content: {
        padding: 25,
        maxWidth: 1040,
    },
    termsCondHead: {
        '& .MuiTypography-subtitle2': {
            lineHeight: '24px',
            fontWeight: 400
        }
    },
    accredLogo: {
        width: 180,
        position: 'absolute',
        top: 0
    },
    tableWraper: {
        width: '100%', 
        marginBottom: 20, 
        paddingRight: 20,
        marginTop: 12,
        '& .MuiTableCell-root': {
            color: '#4D5766',
            border: '1px solid #c8c2c2'
        }
    }
}));

const AccreditationCardItem = ({ accredItem, accredName }) => {
    const classes = useStyles();
    const isMobile = IsMobileDevice('sm');
    return (
        <Grid item xs={12} lg={6} sm={6}>
            <Card className={classes.cardRoot}>
                <CardContent>
                    <Grid container spacing={2}>
                        <Grid item xs={6} sm={4} lg={4}>
                            <div className={(!isMobile) ? "accredCardItemMob" : "accredCardItem"}>
                                <Typography>ID : {accredItem?.courseId}</Typography>
                            </div>
                        </Grid>
                        <Grid item xs={6} sm={5} lg={5}>
                            <div className={(!isMobile) ? "accredCardItemMob" : "accredCardItem"}>
                                <Typography>NQF LEVEL : {accredItem?.nQFLevel}</Typography>
                            </div>
                        </Grid>
                        <Grid item xs={6} sm={3} lg={3}>
                            <div className={(!isMobile) ? "accredCardItemMob" : "accredCardItem"}>
                                <Typography>CREDITS : {accredItem?.credits}</Typography>
                            </div>
                        </Grid>
                    </Grid>
                    <Typography style={{ color: '#4D5766', marginTop: 20 }} className='accredCourseSubTitle'>
                        {accredItem?.unitStandards}
                    </Typography>
                </CardContent>
            </Card>
        </Grid>
    )
}

const AccreditationItemPanel = ({ accredit }) => {
    const history = useHistory();

    return (
        <Grid container>
            <Grid item xs={12}>
                <Typography className='accredCourseTitle'>{accredit.name}</Typography>
                <Typography className='accredCourseSubTitle'>{accredit.subTitle}</Typography>
                <Typography className='accredCourseSubTitle'><b>ACCREDITATION NUMBER: </b> {accredit.id}</Typography>
            </Grid>
            {accredit.list.map((itm) => (
                <React.Fragment key={itm.name}>
                    <Grid item xs={12}>
                        <div className='heading'>
                            {itm.name}
                            {itm.isForBuy &&
                            <Button style={{ backgroundColor: '#FFF', color: '#0080CA', borderRadius: 5 }} onClick={() => history.push((itm?.uuid) ? `/course/${itm?.uuid}` : Paths.CourseListing)} >Buy Now</Button>}
                        </div>
                    </Grid>
                    <Grid container spacing={2}>
                        {itm.items.map((item) => (<AccreditationCardItem key={item.courseId} accredItem={item} />))}
                    </Grid>
                </React.Fragment>
            ))}
        </Grid>
    )
}

const AccreditationTableItemPanel = ({ accredit }) => {
    const classes = useStyles();
    const history = useHistory();
    
    return (
        <Grid container>
            <Grid item xs={12}>
                <Typography className='accredCourseTitle'>{accredit.name}</Typography>
                <Typography className='accredCourseSubTitle'>{accredit.subTitle}</Typography>
                {/* <Typography className='accredCourseSubTitle'><b>ACCREDITATION NUMBER: </b> {accredit.id}</Typography> */}
            </Grid>
        <TableContainer className={classes.tableWraper} >
            <Table>
                <TableHead style={{backgroundColor: '#e1e1e1'}}>
                    <TableRow>
                        <TableCell variant="head" align='center'>Module</TableCell>
                        <TableCell variant="head" align='center'>ID</TableCell>
                        {accredit.name !== 'IITPSA' && <TableCell variant="head" align='center'>Certificates</TableCell>}
                        <TableCell variant="head" align='center'>Action</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {accredit.list.map((itm, idx) => {
                            return  (
                                <TableRow key={itm.name}>
                                    <TableCell align='center'>{itm.name}</TableCell>
                                    <TableCell align='center'>{itm?.items ? itm.items[0]?.courseId : '-'}</TableCell>
                                    {accredit.name !== 'IITPSA' && idx === 0 && <TableCell rowSpan={3} align='center'>{itm?.items && itm.items[0]?.nQFLevel ? `NQF ${itm.items[0]?.nQFLevel}` : '-'}</TableCell>}
                                    {(accredit.name === 'UNISA' && idx === 0) &&
                                        <TableCell rowSpan={3} align='center'><Button onClick={() => history.push(`${Paths.CourseListing}?courseType=Learning Path`)} variant="outlined" color="primary" size='small' style={{ borderRadius: 6, padding: '5px 10px' }}>Buy Now</Button></TableCell>
                                    }
                                    {accredit.name === 'IITPSA' && <TableCell align='center'><Button onClick={() => history.push(`${Paths.CourseListing}?courseType=Learning Path`)} variant="outlined" color="primary" size='small' style={{ borderRadius: 6, padding: '5px 10px' }}>Buy Now</Button></TableCell>}

                                </TableRow>
                            );
                        }
                    )}
                </TableBody>
            </Table>
        </TableContainer>
        </Grid>
    )
}

const QualificationPanel = ({ qualifications, handleSetActiveIndex }) => {

    return (
        <Grid container style={{ marginTop: 20 }}>
            <Grid item xs={12}>
                <CustomTabs
                    handleSetActiveIndex={handleSetActiveIndex}
                    tabs={qualifications.map((itm) => {
                        if(itm.name === 'MICT/SETA'){
                            return {
                                label: itm.name,
                                children: <AccreditationItemPanel accredit={itm} />
                            };
                        }else{
                            return {
                                label: itm.name,
                                children: <AccreditationTableItemPanel accredit={itm} />
                            };
                        }
                    })}
                />
            </Grid>
        </Grid>
    )
}
const customerService = new CustomerServices();

const Accreditations = () => {

    const classes = useStyles();
    const [contactFormState, setContactFormState] = React.useState({ ...initContactFields });
    const [contactFormLoading, setContactFormLoading] = React.useState(false);
    const [activeIndex, setActiveIndex] = React.useState(0);
    const isMobile = IsMobileDevice('sm');
    
    const qualifications = [
        {
            name: 'MICT/SETA',
            subTitle: "MICT SETA is responsible for skills development in the Advertising, Electronic Media & Film, Electronics, Information Technology and Telecommunications sectors. They are responsible for ensuring that the skills needs of these sectors are identified and that adequate and appropriate skills development is provided through several different initiatives.",
            id: 'ACC/2019/07/0020',
            list: accreditationCourses.mict,
            logo: MICT_SETA
        },
        {
            name: 'IITPSA',
            subTitle: "IITPSA is one of the oldest IT societies in South Africa. It is a non-profit organisation that aims to promote professionalism and ethical conduct among IT practitioners. They offer various courses and certifications in IT.",
            id: 'CPD-SUPP-DEV2021',
            list: accreditationCourses.iitpsa,
            logo: IITPSA
        },
        {
            name: 'UNISA',
            subTitle: "UNISA has signed a digital skills partnership deal with Deviare. Through their collaboration, students have access to flexible and high-quality education, making it possible for working professionals to advance their careers and stay competitive in the job market.",
            id: 'ACC/2019/07/0020',
            logo: UNISA,
            list: accreditationCourses?.unisa || []
        }
    ];

    const handleContactSubmit = async () => {
        if (!contactFormState.email || !contactFormState.first_name || !contactFormState.last_name || !contactFormState.mobile_no || !contactFormState.work_status || !contactFormState.study_type || !contactFormState.start_study_time) {
            notify('error', REQUIRED_ERROR);
            return null;
        }
        if (!contactFormState.acceptPolicy) {
            notify('error', 'Please Accept Terms And Condition');
            return null;
        }
        setContactFormLoading(true);
        const postData = { ...contactFormState };
        delete postData.acceptPolicy;
        postData.newsletter = (contactFormState?.sendNewsLatter === 'yes') ? true : false;
        const result = await customerService.sendContactMail(postData);
        if (result) {
            setContactFormState({ ...initContactFields });
            notify('success', 'Your Request is submitted successfully!');
        }
        setContactFormLoading(false);
    }

    return (
        <Layout>
            <main style={{backgroundColor: '#fff'}}>
                <PricingPlans/>
                {/* <div className={classes.bannerWrap}>
                    <Container className={classes.content} >
                        <Grid container spacing={2}>
                            <Grid item xs={12} lg={7}>
                                <Typography className='accredTitle'>Deviare Academy Accreditations</Typography>
                                <Typography className='accredSubTitle'>Deviare Academyis registered with the Department of Higher Education and Training as a private higher education institution. Learn more about our accreditations and memberships.</Typography>
                            </Grid>
                        </Grid>
                    </Container>
                </div>
                <Container className={classes.content}>
                    <Grid container className={classes.innerAcredWrap} spacing={2}>
                        <Grid item xs={7}>
                            <Typography style={(!isMobile) ? { fontSize: 25 } : null } className={'accredTitle'}>Accreditations</Typography>
                        </Grid>
                        <Grid item xs={3}>
                            {qualifications?.[activeIndex] && <img style={(!isMobile) ? { width: 120 } : null } className={classes.accredLogo} src={qualifications[activeIndex].logo} alt='Accreditation Logo..' />}
                        </Grid>
                        <QualificationPanel activeIndex={activeIndex} handleSetActiveIndex={(value) => setActiveIndex(value)} qualifications={qualifications} />
                    </Grid>
                </Container> */}
                <ContactUsPanel
                    isNotRedirectId={true}
                    formState={contactFormState}
                    handleChange={(evt) => {
                        let { name, value } = evt.target;
                        if (name === 'acceptPolicy') {
                            value = evt.target.checked;
                        }
                        setContactFormState({ ...contactFormState, [name]: value });
                    }}
                    loading={contactFormLoading}
                    handleSubmit={handleContactSubmit}
                />
            </main>
        </Layout>
    );
}

export default Accreditations;