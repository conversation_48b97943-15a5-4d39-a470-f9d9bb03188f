import React, { useEffect, useState } from 'react';
import { useParams, useHistory } from 'react-router-dom';
import { 
  Container, 
  Grid, 
  Typography, 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  Divider, 
  CircularProgress, 
  Dialog, 
  IconButton, 
  Snackbar 
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { makeStyles } from '@material-ui/core/styles';
import StarBorderIcon from '@material-ui/icons/StarBorder';
import CheckIcon from '@material-ui/icons/Check';
import CloseIcon from '@material-ui/icons/Close';
import Layout from "Components/layouts";
import { useLocation } from 'react-router-dom/cjs/react-router-dom';
import { useKeycloak } from '@react-keycloak/web';

const useStyles = makeStyles((theme) => ({
  root: {
    backgroundColor: '#f9fbfd',
    paddingBottom: theme.spacing(4),
  },
  header: {
    background: 'linear-gradient(70.3deg, rgb(66 147 156) 14.53%, rgb(12, 78, 118) 70.96%)',
    padding: theme.spacing(3, 0),
    color: '#fff',
  },
  breadcrumb: {
    fontSize: '0.9rem',
    opacity: 0.8,
    marginBottom: theme.spacing(1),
  },
  title: {
    fontWeight: 700,
  },
  starRow: {
    display: 'flex',
    gap: 5,
    marginTop: theme.spacing(1),
  },
  section: {
    marginTop: theme.spacing(4),
  },
  courseOutlineItem: {
    backgroundColor: '#f4f6f8',
    padding: theme.spacing(1.5),
    marginBottom: theme.spacing(1),
    borderRadius: 6,
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: '#e8ebee',
    },
  },
  priceCard: {
    padding: theme.spacing(2),
    textAlign: 'center',
    border: '1px solid #ddd',
    borderRadius: 8,
    marginBottom: theme.spacing(2),
    width: '100%',
    maxWidth: 350,
    margin: '0 auto 16px auto',
  },
  price: {
    fontSize: '1.5rem',
    fontWeight: 600,
    margin: theme.spacing(1, 0),
  },
  btnPrimary: {
    backgroundColor: '#497ec4',
    color: '#fff',
    marginTop: theme.spacing(1),
    '&:hover': {
      backgroundColor: '#125ea2',
    },
    borderRadius: 8,
    padding: theme.spacing(1.5, 3),
    whiteSpace: 'nowrap',
    width: '80%',
    fontWeight: 500,
  },
  accessButton: {
    marginTop: theme.spacing(2),
    '&:hover': {
      backgroundColor: '#487dc2',
    },
    borderRadius: 8,
    padding: theme.spacing(1.5, 3),
    fontWeight: 500,
    minWidth: 200,
    '&:disabled': {
      backgroundColor: '#ccc',
      color: '#666',
    },
  },
  launchButton: {
    backgroundColor: '#17a2b8',
    color: '#fff',
    marginTop: theme.spacing(2),
    '&:hover': {
      backgroundColor: '#138496',
    },
    borderRadius: 8,
    padding: theme.spacing(1.5, 3),
    fontWeight: 500,
    minWidth: 200,
  },
  checkItem: {
    display: 'flex',
    alignItems: 'center',
    gap: 6,
    marginBottom: theme.spacing(1),
  },
  subHeading: {
    fontWeight: 600,
    marginBottom: theme.spacing(1),
  },
  rightSectionContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
  },
  // Iframe modal styles
  iframeDialog: {
    '& .MuiDialog-paper': {
      margin: 0,
      width: '100vw',
      height: '100vh',
      maxWidth: 'none',
      maxHeight: 'none',
      borderRadius: 0,
    },
  },
  iframeContainer: {
    position: 'relative',
    width: '100%',
    height: '100vh',
    display: 'flex',
    flexDirection: 'column',
  },
  iframeHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(1, 2),
    backgroundColor: '#f5f5f5',
    borderBottom: '1px solid #ddd',
    minHeight: 56,
  },
  iframeTitle: {
    fontWeight: 600,
    color: '#333',
  },
  closeButton: {
    color: '#666',
  },
  iframe: {
    flex: 1,
    width: '100%',
    border: 'none',
    backgroundColor: '#fff',
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    fontSize: '18px',
    color: '#666',
  },
}));

const AccessCourseDetail = () => {
  // State for expanded course outline sections
  const [outlineOpen, setOutlineOpen] = useState([]);
  const classes = useStyles();
  const query = new URLSearchParams(useLocation().search);
  const [course, setCourse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [profileData, setProfileData] = useState(null);
  const [iframeOpen, setIframeOpen] = useState(false);
  const [iframeUrl, setIframeUrl] = useState('');
  const [iframeLoading, setIframeLoading] = useState(false);
  const [accessLoading, setAccessLoading] = useState(false);
  
  // Snackbar states
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' // success, error, warning, info
  });
  
  const id = query.get("course_id");
  const { keycloak } = useKeycloak();
  const history = useHistory();
  
  // Get user ID from localStorage
  const userId = localStorage.getItem('uuid') || '26469e57-9cfa-42dc-8ea3-32d80ecb0c74';
  
  // Simplified login check
  const isLoggedIn = Boolean(
    localStorage.getItem('token') || 
    keycloak?.authenticated
  );

  // Snackbar helper functions
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  const handleCloseSnackbar = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Fetch user profile data on component mount
  useEffect(() => {
    if (isLoggedIn) {
      fetchProfileData();
    }
  }, [isLoggedIn]);

  const fetchProfileData = async () => {
    try {
      const response = await fetch('https://api-staging.deviare.africa/main/myprofile', {
        method: 'GET',
        headers: {
          'accept': 'application/json, text/plain, */*',
          'authorization': `Token ${localStorage.getItem('token')}`,
        }
      });
      
      const data = await response.json();
      
      if (data.status && data.data && data.data.length > 0) {
        setProfileData(data.data[0]);
        console.log('Profile data loaded:', data.data[0]);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  const checkCustomerCourses = async (userId) => {
    try {
      console.log(`Checking customer courses for user ID: ${userId}`);
      const response = await fetch(`https://api-staging.deviare.africa/main/CustomerCourses/${userId}`, {
        method: 'GET',
        headers: {
          'accept': 'application/json',
          'authorization': `Token ${localStorage.getItem('token')}`,
          'content-type': 'application/json',
        }
      });
      const data = await response.json();
      console.log('Customer courses API response:', data);
      if (data.success && data.data && data.data.courses && data.data.courses.length > 0) {
        console.log('User has existing courses, redirecting to /account/course');
        history.push('/account/course');
        return true;
      }
      console.log('User has no existing courses');
      return false;
    } catch (error) {
      console.error('Error checking customer courses:', error);
      return false;
    }
  };

  // Function to open subscription iframe
  const openSubscriptionIframe = (planId, planType = 'lifetime') => {
    if (!profileData) {
      showSnackbar('Please log in to continue with the purchase.', 'warning');
      return;
    }

    const firstName = profileData.firstName || profileData.first_name || 'User';
    const lastName = profileData.lastName || profileData.last_name || '';
    const email = profileData.email || '';

    if (!email) {
      showSnackbar('Email not found. Please update your profile and try again.', 'error');
      return;
    }

    // Build subscription URL with plan_id and course_id
    const subscriptionUrl = `https://deviare.subscriptionflow.com/en/hosted-page/subscribe/${planId}/product/${id}?ai_firstName=${encodeURIComponent(firstName)}&ai_lastName=${encodeURIComponent(lastName)}&ai_email=${encodeURIComponent(email)}`;
    
    console.log(`Opening ${planType} subscription URL in iframe:`, subscriptionUrl);
    setIframeUrl(subscriptionUrl);
    setIframeLoading(true);
    setIframeOpen(true);
  };

  const handleBuyCourse = async () => {
    console.log('Buy course clicked, isLoggedIn:', isLoggedIn);
    
    if (!isLoggedIn) {
      console.log('User not logged in, redirecting to Keycloak login');
      keycloak.login();
      return;
    }

    // Get UUID from localStorage
    const userId = localStorage.getItem('uuid');
    
    if (!userId) {
      console.error('No user ID found in localStorage');
      showSnackbar('User session not found. Please log in again.', 'error');
      return;
    }

    // Check if user already has courses
    const hasExistingCourses = await checkCustomerCourses(userId);
    
    if (!hasExistingCourses) {
      // Check if lifetime plan exists in pricing data
      if (course?.pricing?.lifetime_plan?.plan_id) {
        // Open iframe with lifetime plan
        openSubscriptionIframe(course.pricing.lifetime_plan.plan_id, 'lifetime');
      } else {
        console.error('Lifetime plan not found in course pricing data');
        showSnackbar('Lifetime purchase option is not available for this course.', 'error');
      }
    }
    // If hasExistingCourses is true, navigation already happened in checkCustomerCourses
  };

  const handleFreeTrial = async () => {
    console.log('Free trial clicked, isLoggedIn:', isLoggedIn);
    
    if (!isLoggedIn) {
      console.log('User not logged in, redirecting to Keycloak login');
      keycloak.login();
      return;
    }

    // Get UUID from localStorage
    const userId = localStorage.getItem('uuid');
    
    if (!userId) {
      console.error('No user ID found in localStorage');
      // Still proceed to plans page as fallback
      history.push(`/plans?course_id=${id}`);
      return;
    }

    // Check if user already has courses
    const hasExistingCourses = await checkCustomerCourses(userId);
    
    if (!hasExistingCourses) {
      // No existing courses, proceed to plans page
      console.log('Proceeding to plans page');
      history.push(`/plans?course_id=${id}`);
    }
    // If hasExistingCourses is true, navigation already happened in checkCustomerCourses
  };

  // Updated Access Course handler with API call
  const handleAccessCourse = async () => {
    console.log('Access course clicked');
    
    if (!isLoggedIn) {
      showSnackbar('Please log in to access the course.', 'warning');
      keycloak.login();
      return;
    }

    if (!userId || !id) {
      showSnackbar('Missing user or course information. Please try again.', 'error');
      return;
    }

    setAccessLoading(true);

    try {
      console.log('Assigning course to user:', { course_id: id, user_id: userId });
      
      const response = await fetch('https://api-staging.deviare.africa/main/AssignCourseToUser/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Token ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          course_id: id,
          user_id: userId
        })
      });

      const data = await response.json();
      console.log('AssignCourseToUser API response:', data);

      if (response.ok) {
        // Success - show success message and redirect
        showSnackbar('Course assigned successfully! You can now access the course.', 'success');
        
        // Redirect to user courses page or course launch
        setTimeout(() => {
           window.location.reload();
        }, 2000);
        
      } else {
        // Handle API errors
        const errorMessage = data.message || data.error || 'Failed to assign course. Please try again.';
        showSnackbar(errorMessage, 'error');
        console.error('API Error:', data);
      }

    } catch (error) {
      console.error('Error assigning course to user:', error);
      
      let errorMessage = 'Network error. Please check your connection and try again.';
      
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        errorMessage = 'Connection failed. Please check your internet connection.';
      }
      
      showSnackbar(errorMessage, 'error');
    } finally {
      setAccessLoading(false);
    }
  };

  // Handle iframe load
  const handleIframeLoad = () => {
    setIframeLoading(false);
  };

  // Handle closing iframe
  const handleCloseIframe = () => {
    setIframeOpen(false);
    setIframeUrl('');
    setIframeLoading(false);
  };

  // Listen for messages from iframe for subscription completion
  useEffect(() => {
    const handleMessage = (event) => {
      // Security check - only accept messages from subscription domain
      if (!event.origin.includes('deviare.subscriptionflow.com')) {
        return;
      }
      
      console.log('Received message from iframe:', event.data);
      
      // Handle subscription success
      if (event.data && event.data.type === 'subscription_success') {
        console.log('Subscription successful!');
        handleCloseIframe();
        showSnackbar('Purchase completed successfully! You now have access to the course.', 'success');
        // Optionally redirect to course or account page
        // history.push('/account/course');
      }
      
      // Handle subscription cancellation
      if (event.data && event.data.type === 'subscription_cancelled') {
        console.log('Subscription cancelled');
        handleCloseIframe();
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [history]);

  // Updated fetch course details with user_id parameter
  useEffect(() => {
    console.log(`Fetching course details for ID: ${id} with user ID: ${userId}`);
    
    const fetchUrl = `https://api-staging.deviare.africa/main/coursedetails/${id}?user_id=${userId}`;
    console.log('Fetching from URL:', fetchUrl);
    
    fetch(fetchUrl)
      .then(res => res.json())
      .then(data => {
        console.log('Course data received:', data);
        if (data.status && data.data) {
          setCourse(data.data);
        } else {
          setError('Failed to load course details');
        }
        setLoading(false);
      })
      .catch((error) => {
        console.error('Course fetch error:', error);
        setError('Failed to load course details');
        setLoading(false);
      });
  }, [id, userId]);

  if (loading) return <CircularProgress style={{ display: 'block', margin: 'auto', marginTop: '20%' }} />;
  
  if (error || !course) {
    return (
      <Layout>
        <Container>
          <Typography color="error" align="center" style={{ marginTop: '20%' }}>
            {error || 'Course not found'}
          </Typography>
        </Container>
      </Layout>
    );
  }

  // Check if course has a link (course is available)
  const hasLink = course.link && course.link.trim() !== '';
  console.log('Course link status:', { link: course.link, hasLink });

  return (
    <Layout>
      <div className={classes.root}>
        {/* Header */}
        <div className={classes.header} style={{height: '300px'}}>
          <Container>
            <Typography variant="h3" className={classes.title} style={{color:"white"}}>
              {course.name || 'Course Title'}
            </Typography>
            <div className={classes.starRow}>
              {[...Array(5)].map((_, i) => (
                <StarBorderIcon key={i} style={{ color: '#fff' }} />
              ))}
            </div>
          </Container>
        </div>

        <Container>
          <Grid container spacing={4} className={classes.section}>
            {/* Left Section */}
            <Grid item xs={12} md={8}>
              <Typography variant="h6" style={{ fontWeight: 600, marginBottom: 8, color:"#4d81bf" }}>
                Course Description
              </Typography>
              <Typography variant="body1" paragraph>
                {course.course_description || 'Course description will be available soon.'}
              </Typography>

              {/* Key Learning Objectives */}
              <Typography variant="h6" className={classes.subHeading} style={{color:'#4d81bf', marginTop: 50}}>
                Key Learning Objectives
              </Typography>
              {Array.isArray(course.course_objectives) ? (
                <Grid container spacing={2}>
                  {course.course_objectives
                    .flatMap(obj => obj.split('•').map(s => s.trim()).filter(Boolean))
                    .map((objective, idx) => (
                      <Grid item xs={12} sm={6} key={idx}>
                        <div className={classes.checkItem}>
                          <CheckIcon style={{ color: '#4d81bf', fontSize: 18 }} />
                          <Typography variant="body2">{objective}</Typography>
                        </div>
                      </Grid>
                    ))}
                </Grid>
              ) : (
                <Typography variant="body2" paragraph>
                  {course.course_objectives || 'Learning objectives will be available soon.'}
                </Typography>
              )}

              {/* Course Outline */}
              <Typography variant="h6" className={classes.subHeading} style={{color:'#4d81bf', marginTop: 50}}>
                Course Outline
              </Typography>
              {Array.isArray(course.course_outline) ? (
                course.course_outline.map((section, idx) => {
                  const [title, ...rest] = section.split('•');
                  const points = rest.map(s => s.trim()).filter(Boolean);
                  const open = outlineOpen[idx] || false;
                  const handleToggle = () => {
                    setOutlineOpen(prev => {
                      const copy = [...prev];
                      copy[idx] = !copy[idx];
                      return copy;
                    });
                  };
                  return (
                    <div key={idx} style={{marginBottom: 12}}>
                      <div
                        className={classes.courseOutlineItem}
                        style={{cursor: points.length ? 'pointer' : 'default', display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}
                        onClick={() => points.length && handleToggle()}
                      >
                        <Typography variant="subtitle1" style={{fontWeight: 600}}>{title}</Typography>
                        {points.length > 0 && (
                          <span style={{fontSize: 18}}>{open ? '▲' : '▼'}</span>
                        )}
                      </div>
                      {open && points.length > 0 && (
                        <ul style={{marginLeft: 24, marginTop: 8}}>
                          {points.map((point, i) => (
                            <li key={i}>
                              <Typography variant="body2">{point}</Typography>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  );
                })
              ) : (
                <Typography variant="body2">
                  {course.course_outline || 'Course outline will be available soon.'}
                </Typography>
              )}

              {/* Intended Audience */}
              <Typography variant="h6" className={classes.subHeading} style={{color:'#4d81bf', marginTop: 50}}>
                Intended Audience
              </Typography>
              {Array.isArray(course.intended_audience) ? (
                <Grid container spacing={2}>
                  {course.intended_audience
                    .flatMap(aud => aud.split('•').map(s => s.trim()).filter(Boolean))
                    .map((aud, idx) => (
                      <Grid item xs={12} sm={6} key={idx}>
                        <div className={classes.checkItem}>
                          <CheckIcon style={{ color: '#4d81bf', fontSize: 18 }} />
                          <Typography variant="body2">{aud}</Typography>
                        </div>
                      </Grid>
                    ))}
                </Grid>
              ) : (
                <Typography variant="body2">
                  {course.intended_audience || 'Intended audience information will be available soon.'}
                </Typography>
              )}

              {/* Prerequisites */}
              <Typography variant="h6" className={classes.subHeading} style={{color:"#4d81bf", marginTop: 50}}>
                Prerequisites
              </Typography>
              <Typography variant="body2">
                {course.prerequisites || 'Prerequisites information will be available soon.'}
              </Typography>
            </Grid>

            {/* Right Section - Conditional Rendering */}
            <Grid item xs={12} md={4}>
              <div className={classes.rightSectionContainer}>
                {hasLink ? (
                  // Show only Launch Course button when link exists
                  <div style={{ textAlign: 'center', marginTop: 20 }}>
                    <Typography variant="h6" style={{ marginBottom: 16, fontWeight: 600 }}>
                      Ready to Start?
                    </Typography>
                    <Button 
                      variant="contained" 
                      className={classes.launchButton}
                      href={course.link} 
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Launch Course
                    </Button>
                  </div>
                ) : (
                  // Show Access Course button when link is null
                  <div style={{ textAlign: 'center', marginTop: 20 }}>
                    <Button 
                      variant="contained" 
                      color="primary"
                      className={classes.accessButton}
                      onClick={handleAccessCourse}
                      disabled={accessLoading}
                    >
                      {accessLoading ? (
                        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                          <CircularProgress size={16} color="inherit" />
                          Assigning Course...
                        </div>
                      ) : (
                        'Access Course'
                      )}
                    </Button>
                    <Typography variant="body2" style={{ marginTop: 12, color: '#666', fontSize: '0.85rem' }}>
                      Click to get access to this course
                    </Typography>
                  </div>
                )}
              </div>
            </Grid>
          </Grid>
        </Container>

        {/* Snackbar for notifications */}
        <Snackbar 
          open={snackbar.open} 
          autoHideDuration={6000} 
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        >
          <Alert 
            onClose={handleCloseSnackbar} 
            severity={snackbar.severity} 
            variant="filled"
          >
            {snackbar.message}
          </Alert>
        </Snackbar>

        {/* Full Screen Iframe Dialog */}
        <Dialog
          open={iframeOpen}
          onClose={handleCloseIframe}
          className={classes.iframeDialog}
          fullScreen
        >
          <div className={classes.iframeContainer}>
            <div className={classes.iframeHeader}>
              <Typography className={classes.iframeTitle}>
                Complete Your Purchase - {course.pricing?.lifetime_plan?.plan_name || 'Lifetime Access'}
              </Typography>
              <IconButton 
                className={classes.closeButton}
                onClick={handleCloseIframe}
                aria-label="close"
              >
                <CloseIcon />
              </IconButton>
            </div>
            {iframeLoading && (
              <div className={classes.loadingContainer}>
                Loading purchase page...
              </div>
            )}
            {iframeUrl && (
              <iframe
                src={iframeUrl}
                className={classes.iframe}
                title="Purchase Page"
                onLoad={handleIframeLoad}
                allow="payment; camera; microphone"
                sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
              />
            )}
          </div>
        </Dialog>
      </div>
    </Layout>
  );
};

export default AccessCourseDetail;
