.Product {
    &__Carousel {
        position: relative;
        grid-column: span 12;
        margin-top: 20px;
        width: 100%;
    }
    &__Navigation {
        position: absolute;
        right: 0px;
        top: -3rem;
        display: flex;
        &__button {
            &--disabled {
                color: rgba(128, 128, 128, 0.596);
            }

            &--hidden {
                display: none;
            }

            &:focus-visible {
                outline: thin auto Highlight;
                outline: thin auto -webkit-focus-ring-color;
            }
        }
    }
    &__Navigation_Brand {
        position: absolute;
        right: 0px;
        top: -6rem;
        display: flex;
        &__button {
            &--disabled {
                color: rgba(128, 128, 128, 0.596);
            }

            &--hidden {
                display: none;
            }

            &:focus-visible {
                outline: thin auto Highlight;
                outline: thin auto -webkit-focus-ring-color;
            }
        }
    }
}