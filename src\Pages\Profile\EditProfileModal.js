import React, { useRef } from 'react';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import makeStyles from '@material-ui/core/styles/makeStyles';
import TextField from '@material-ui/core/TextField';
import Grid from '@material-ui/core/Grid';
import Box from '@material-ui/core/Box';
import Typography from '@material-ui/core/Typography';
import IconButton from '@material-ui/core/IconButton';
import CircularProgress from '@material-ui/core/CircularProgress';
import AutocompleteSelect from '@material-ui/lab/Autocomplete';
import CloseIcon from '@material-ui/icons/Close';
import { DialogActions, Input } from '@material-ui/core';
import PhoneInputField from 'Components/PhoneInputField';
import OutlinedSelect from 'Components/OutlinedSelect';
import Languages from '../../utils/languages';
import { Industries, OCCUPATIONS } from 'utils/constants';
import CustomAutoComplete from 'Components/Autocomplete';
import AutocompleteAddress from 'Components/AutocompleteAddress';
import { getFullAddress } from 'utils';

export const marketingPrefOpt = ["Email", "SMS", "Phone", "Push Notifications"]; 
export const referralSourcesOpt = ["Friends/Family", "Online Search", "Social Media", "Advertisements", "Event"]; 

const useStyles = makeStyles(theme => ({
    inputStyle: {
        borderColor: '#bebaba',
        minWidth: 180,
        borderRadius: '25px',
    },
    pageHeader: {
        display: 'flex',
        justifyContent: 'space-between',
        margin: '25px 0'
    },
    heading: {
        fontSize: '23px',
        fontWeight: '500'
    },
    btn: {
        padding: '13px 35px',
        fontSize: '13px',
        color: 'white',
        borderRadius: '5px',
        marginLeft: '15px',
    },
    cancelBtn: {
        padding: '13px 35px',
        background: '#B7B7B7',
        fontSize: '13px',
        color: 'white',
        borderRadius: '5px',
        marginLeft: '15px',
        "&:hover": {
            background: "#454343"
        }
    },
    placeBtn: {
        display: 'flex',
        justifyContent: 'end'
    },
    imgIcon: {
        width: 130,
        borderRadius: '50%',
    },
    input: {
        display: 'none'
    },
    icon: {
        position: 'relative',
        margin: '1px auto 24px',
        background: '#090909',
        borderRadius: '50%',
        maxWidth: 136,
        textAlign: 'center'
    },
    iconStyle: {
        borderRadius: '50%',
        color: '#000',
        background: '#fff',
        border: '1px solid #808080'
    },
    iconbtn: {
        bottom: '-17px',
        left: '51px',
        position: 'absolute'
    },
    closeBtn: {
        position: 'absolute',
        top: 20,
        right: 20
    },
    phoneInputStyle: {
        marginBottom: 15,
        marginTop: 8,
        position: 'relative',
        '& .PhoneInputInput':{
            border: '1px solid #bebaba',
            borderRadius: '25px',
            padding: 16,
            paddingLeft: 48
        },
        '& .PhoneInputCountry': {
            position: 'absolute',
            left: 12,
            top: 14
        }
    },
    occupationDropDown: {
        '& .MuiFormControl-root .MuiAutocomplete-inputRoot': {
            border: '1px solid #bebaba !important',
            borderRadius: 25,
            padding: 4,
            '&:hover': {
                border: 'none !important'
            }
        }
    }
}));

const EditProfileModal = ({ 
    open, 
    handleClose, 
    handleSubmit, 
    formState, 
    handleInputChange, 
    countryList,
    handleChange,
    handleAutoSelectAddress,
    loading 
}) => {

    const classes = useStyles();
    const autocompleteRef = useRef();

    function handlePlaceChanged() {
        const [ place ] = autocompleteRef.current.getPlaces();
        if(place) { 
            const {postalCode, country, province, locality } = getFullAddress(place);
            handleAutoSelectAddress({ pincode: postalCode || formState.pincode, country: country || formState.country, province: province || formState.province, city: locality || formState.city, address: place?.name || formState.address });
        }
    }

    return (
        <div>
            <Dialog
                open={open}
                style={{ borderRadius: 5 }}
                fullWidth={true}
                maxWidth={'md'}
                onClose={handleClose}
                aria-labelledby="alert-dialog-slide-title"
                aria-describedby="alert-dialog-slide-description"
            >
                <DialogTitle id="alert-dialog-slide-title" style={{ position: 'relative' }}>
                    <Typography style={{ lineHeight: 2.5 }} variant="h1">
                        Update Profile
                    </Typography>
                    <IconButton className={classes.closeBtn} onClick={handleClose}><CloseIcon /></IconButton>
                </DialogTitle>
                <DialogContent>
                    <Box component="form" id="loginForm" onSubmit={handleSubmit} className={classes.formBox} >
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 10, marginTop: '15px' }}>
                                    First Name *
                                </Typography>
                                <Input
                                    disableUnderline
                                    value={formState.firstName}
                                    type={'text'}
                                    name="firstName"
                                    className={classes.inputStyle}
                                    placeholder={"First Name"}
                                    style={{ width: '100%' }}
                                    color={'secondary'}
                                    onChange={handleInputChange}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 10, marginTop: '15px' }}>
                                    Last Name *
                                </Typography>
                                <Input 
                                    disableUnderline 
                                    value={formState.lastName}
                                    type={'text'}
                                    className={classes.inputStyle}
                                    placeholder={"Last Name"}
                                    style={{ width: '100%' }}
                                    name="lastName"
                                    color={'secondary'}
                                    onChange={handleInputChange}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 10, marginTop: '15px' }}>
                                    Username *
                                </Typography>
                                <Input
                                    disableUnderline
                                    disabled
                                    value={formState.userName}
                                    type={'text'}
                                    placeholder={"Member Since"}
                                    className={classes.inputStyle}
                                    style={{ width: '100%' }}
                                    color={'secondary'}
                                />
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 10, marginTop: '15px' }}>
                                    Email *
                                </Typography>
                                <Input disableUnderline value={formState.email}
                                    type={'text'}
                                    disabled={true}
                                    className={classes.inputStyle}
                                    placeholder={"Email"}
                                    style={{ width: '100%' }}
                                    color={'secondary'}
                                    name="email"
                                    onChange={handleInputChange}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 10, marginTop: '15px' }}>
                                    Phone Number *
                                </Typography>
                                <PhoneInputField
                                    handleChange={(newVal) => handleInputChange({ target: { value: newVal, name: 'contact_no' } })}
                                    value={(formState?.contact_no) ? formState?.contact_no?.toString() : ""}
                                    name="contact_no"
                                    customClass={classes.phoneInputStyle}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 10, marginTop: '15px' }}>
                                    Address *
                                </Typography>
                                <AutocompleteAddress 
                                    autocompleteRef={autocompleteRef} 
                                    handlePlaceChanged={handlePlaceChanged} 
                                />
                                <Typography style={{ marginBottom: 10, marginTop: 5 }} variant="subtitle2" color="textPrimary">{formState.address}</Typography>
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 10, marginTop: '15px' }}>
                                    Postal Code *
                                </Typography>
                                <Input 
                                    disableUnderline 
                                    value={formState.pincode}
                                    type={'text'}
                                    placeholder={"Postal Code"}
                                    className={classes.inputStyle}
                                    style={{ width: '100%' }}
                                    color={'secondary'}
                                    name="pincode"
                                    onChange={handleInputChange}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 10, marginTop: '15px' }}>
                                    Country *
                                </Typography>
                                <AutocompleteSelect
                                    id="combo-box-demo4"
                                    value={formState.country}
                                    options={countryList ? countryList : []}
                                    getOptionLabel={option => option}
                                    onChange={(event, newValue) => {
                                        handleChange(newValue, 'country');
                                    }}
                                    style={{ minWidth: 180, width: "100%" }}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            variant="standard"
                                            color="secondary"
                                            placeholder="Select Country"
                                            InputProps={{
                                                ...params.InputProps,
                                                className: classes.inputStyle,
                                                disableUnderline: true
                                            }}
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 10, marginTop: '15px' }}>
                                    City *
                                </Typography>
                                <Input
                                    disableUnderline
                                    value={formState.city}
                                    type={'text'}
                                    className={classes.inputStyle}
                                    placeholder={"City"}
                                    style={{ width: '100%' }}
                                    color={'secondary'}
                                    name="city"
                                    onChange={handleInputChange}
                                />
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 10, marginTop: '15px' }}>
                                    Province *
                                </Typography>
                                <Input
                                    disableUnderline
                                    value={formState.province}
                                    type={'text'}
                                    name="province"
                                    className={classes.inputStyle}
                                    placeholder={"Province"}
                                    style={{ width: '100%' }}
                                    color={'secondary'}
                                    onChange={handleInputChange}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 10, marginTop: '15px' }}>
                                    Date Of Birth *
                                </Typography>
                                <Input
                                    disableUnderline
                                    value={formState.dateOfBirth}
                                    type={'date'}
                                    className={classes.inputStyle}
                                    style={{ width: '100%' }}
                                    color={'secondary'}
                                    name="dateOfBirth"
                                    onChange={handleInputChange}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 6, marginTop: '15px' }}>
                                    Industry
                                </Typography>
                                <CustomAutoComplete
                                    id="combo-box-demo4"
                                    val={formState.industry}
                                    options={Industries}
                                    getOptionLabel={option => option}
                                    placeholder={"Select Industry"}
                                    handleChange={(event, newValue) => handleChange(newValue, 'industry')}
                                    styleOverrides={{ minWidth: 180, width: "100%", marginTop: 8 }}
                                    customClass={classes.occupationDropDown}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 6, marginTop: '15px' }}>
                                    Preferred Language
                                </Typography>
                                <OutlinedSelect
                                    val={formState.language}
                                    options={Languages}
                                    selectStyle={{ minWidth: 180 }}
                                    name="language"
                                    styleOverrides={{ minWidth: '100%' }}
                                    color={'secondary'}
                                    handleChange={handleInputChange}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 5, marginTop: '15px' }}>
                                    Marketing Preferences
                                </Typography>
                                <OutlinedSelect
                                    val={formState.marketingPreferences}
                                    options={marketingPrefOpt}
                                    selectStyle={{ minWidth: 180 }}
                                    styleOverrides={{ minWidth: '100%', marginTop: 0 }}
                                    name="marketingPreferences"
                                    color={'secondary'}
                                    handleChange={handleInputChange}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 6, marginTop: '15px' }}>
                                    Referral Source
                                </Typography>
                                <OutlinedSelect
                                    val={formState.referralSource}
                                    options={referralSourcesOpt}
                                    selectStyle={{ minWidth: 180 }}
                                    name="referralSource"
                                    styleOverrides={{ minWidth: '100%' }}
                                    color={'secondary'}
                                    handleChange={handleInputChange}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6} lg={4}>
                                <Typography
                                    variant="subtitle2"
                                    color="textPrimary"
                                    style={{ marginBottom: 10, marginTop: '15px' }}>
                                    Occupation
                                </Typography>
                                <CustomAutoComplete
                                    id="combo-box-demo4"
                                    val={formState?.occupation}
                                    options={OCCUPATIONS}
                                    getOptionLabel={option => option}
                                    placeholder={"Select Occupation"}
                                    handleChange={(event, newValue) => handleChange(newValue, 'occupation')}
                                    styleOverrides={{ minWidth: 180, width: "100%", marginTop: 8 }}
                                    customClass={classes.occupationDropDown}
                                />
                            </Grid>
                        </Grid>
                    </Box>
                </DialogContent>
                <DialogActions>
                    <div className={classes.placeBtn}>
                        <Button onClick={handleClose} color="primary" className={classes.cancelBtn}>
                            Cancel
                        </Button>
                        <Button
                            color="primary"
                            variant="outlined"
                            className={classes.btn}
                            onClick={handleSubmit}>
                            {loading ? <CircularProgress size={15} color="secondary" /> : 'Update'}
                        </Button>
                    </div>
                </DialogActions>
            </Dialog>
        </div>
    );
}

export default EditProfileModal;