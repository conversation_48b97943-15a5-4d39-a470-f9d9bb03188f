import React from 'react';
import Container from '@material-ui/core/Container';
import { makeStyles } from '@material-ui/core/styles';
import Header from "./Header";
import { useAuthDispatch, useAuthState } from 'Context';
import ExHeader from 'Components/ExternalLayout/ExHeader';
import LoginDialogBox from '../../Pages/LoginDialogBox';
import SignUpDialogBox from '../../Pages/SignUpDialogBox';
import Footer from './Footer';
import { useKeycloak } from '@react-keycloak/web';
import CircularProgress from '@material-ui/core/CircularProgress';
import { initOptions } from 'config/keycloak';

const useStyles = makeStyles(() => ({
  content: {
    width: '100%',
    padding: '0px',
    minHeight: 380,
    backgroundColor: '#fff'
  },
  childContent: {
    padding: '25px',
    paddingLeft: '35px'
  }
}));

const Layout = ({ openModalAction, handleEmptyModalAction, onLogin, children }) => {
  const classes = useStyles();
  const dispatch = useAuthDispatch();
  const { userDetails } = useAuthState(dispatch);
  const { keycloak, initialized } = useKeycloak();
  const [loginOpen, setLoginOpen] = React.useState(false);
  const [signUpOpen, setSignUpOpen] = React.useState(false);
  const [isAuthenticating, setIsAuthenticating] = React.useState(true);

  React.useEffect(() => {
    if (keycloak?.authenticated) {
      // Wait for user details to be loaded
      const timer = setTimeout(() => {
        setIsAuthenticating(false);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setIsAuthenticating(false);
    }
  }, [keycloak?.authenticated, userDetails]);

  // Show loading while authenticating
  if (isAuthenticating || !initialized) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        <CircularProgress />
      </div>
    );
  }

  // Only render header after authentication state is confirmed
  const showAuthenticatedHeader = keycloak?.authenticated && userDetails?.uuid;

  return (
    <>
      {showAuthenticatedHeader ? (
        <Header />
      ) : (
        <ExHeader
          handleOpenModal={(type) => {
            if (type === 'login') {
              onLogin();
            } else {
              setSignUpOpen(true);
            }
          }}
        />
      )}
      <Container maxWidth="xlg" className={classes.content} >
        {children}
      </Container>
      <Footer />
      {!userDetails?.uuid && <>
        <LoginDialogBox
          open={loginOpen}
          handleOpenSignUp={() => {
            setSignUpOpen(true);
            setLoginOpen(false);
          }}
          handleClose={() => {
            setLoginOpen(false);
            handleEmptyModalAction();
          }}
        />
        <SignUpDialogBox
          open={signUpOpen}
          handleOpenLogin={async () => {
            // setSignUpOpen(false);
            // setLoginOpen(true);
            try {
              if (!initialized) {
                await keycloak.init(initOptions);
              }

              await keycloak.login({
                redirectUri: `${window.location.origin}/home`,
                prompt: 'login',
                scope: 'openid'
              });
            } catch (error) {
              console.error('Login error:', error);
              // Fallback login attempt without iframe checking
              keycloak.login({
                redirectUri: `${window.location.origin}/home`,
                prompt: 'login',
                scope: 'openid',
                checkLoginIframe: false
              });
            }
          }}
          handleClose={() => {
            setSignUpOpen(false);
            handleEmptyModalAction();
          }}
        />
      </>}
    </>
  );
}

export default Layout;