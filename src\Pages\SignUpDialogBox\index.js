import React, { useEffect, useState } from 'react';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import Slide from '@material-ui/core/Slide';
import makeStyles from '@material-ui/core/styles/makeStyles';
import Grid from '@material-ui/core/Grid';
import Box from '@material-ui/core/Box';
import Typography from '@material-ui/core/Typography';
import FormLabel from '@material-ui/core/FormLabel';
import IconButton from '@material-ui/core/IconButton';
import { Visibility, VisibilityOff } from '@material-ui/icons';
import { checkValidEmail, notify } from '../../utils';
import { ERROR_MESSAGE, Industries, OCCUPATIONS, REQUIRED_ERROR, SUCCESS_MESSAGE } from '../../utils/constants';
import { loginUser, logout, useAuthDispatch } from '../../Context';
import { CircularProgress, Input, InputAdornment } from '@material-ui/core';
import { useHistory, useLocation } from 'react-router-dom';
import CloseIcon from '@material-ui/icons/Close';
import { authService } from '../../Services/AuthService';
import PhoneInputField from 'Components/PhoneInputField';
import OutlinedSelect from 'Components/OutlinedSelect';
import { marketingPrefOpt, referralSourcesOpt } from 'Pages/Profile/EditProfileModal';
import Autocomplete from 'Components/Autocomplete';

const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="left" ref={ref} {...props} />;
});

const useStyles = makeStyles(() => ({
    formBox: {
        width: "100%",
        textAlign: "left"
    },
    inputStyle: {
        borderColor: '#bebaba',
        minWidth: 180,
        borderRadius: '25px',
        marginTop: 8,
        marginBottom: 15,
        padding: 12
    },
    formLabel: {
        fontWeight: "600",
        fontSize: 12,
        lineHeight: '15px',
        letterSpacing: "1px",
        textTransform: "uppercase",
        color: "#b1b1b1"
    },
    loginBtn: {
        padding: 18,
        fontSize: 14,
        fontWeight: 600,
        borderRadius: 5
    },
    eyeIconBtn: {
        position: "absolute",
        right: 10,
        top: 22,
        width: 25,
        height: 25,
        borderRadius: 15
    },
    closeBtn: {
        position: "absolute",
        top: 6,
        right: 6
    },
    orLines: {
        marginTop: 25,
        display: 'inline-block',
        position: 'relative',
        width: '100%'
    },
    line1: {
        position: 'absolute',
        borderTop: '1px solid #9FA2B4',
        width: 165,
        top: 0,
        left: 0
    },
    line2: {
        position: 'absolute',
        borderTop: '1px solid #9FA2B4',
        width: 155,
        top: 0,
        right: 0
    },
    orText: {
        position: 'absolute',
        top: '-10px',
        left: '50%',
        fontSize: 14,
        lineHeight: '20px'
    },
    phoneInputStyle: {
        marginBottom: 15,
        marginTop: 8,
        position: 'relative',
        '& .PhoneInputInput': {
            border: '1px solid #bebaba',
            borderRadius: '25px',
            padding: 13,
            paddingLeft: 48
        },
        '& .PhoneInputCountry': {
            position: 'absolute',
            left: 12,
            top: 14
        }
    },
    occupationDropDown: {
        '& .MuiFormControl-root .MuiAutocomplete-inputRoot': {
            border: '1px solid #bebaba !important',
            borderRadius: 25,
            padding: 4,
            '&:hover': {
                border: 'none !important'
            }
        }
    }
}));

const SignUpDialogBox = ({ open, handleClose, handleOpenLogin }) => {

    const classes = useStyles();
    const dispatch = useAuthDispatch();
    const history = useHistory();
    const location = useLocation();
    const [loading, setLoading] = useState(false);
    const [formState, setFormState] = useState({ contact_no: '', industry: '', referral_source: '', occupation: '', marketing_preferences: '' });

    useEffect(() => {
        logout(dispatch);
        // eslint-disable-next-line
    }, []);

    useEffect(() => {
        if (open) {
            document.getElementById("signupForm").reset();
        }
    }, [open]);

    const [showPassword, setShowPassword] = useState(false);

    const handleInputChange = (evt) => {
        const { name, value } = evt.target;
        setFormState({ ...formState, [name]: value });
    }

    const handleSubmit = async (event) => {
        event.preventDefault();
        const data = new FormData(event.currentTarget);
        const formData = {
            firstName: data.get('first_name'),
            lastName: data.get('last_name'),
            email: data.get('email'),
            userName: data.get('email'),
            confPassword: data.get('conf_password'),
            password: data.get('password'),
            customers: [process.env.REACT_APP_DEVIARE_COMPANY_ID],
            project_id: process.env.REACT_APP_DEVIARE_PROJECT_ID
        };
        if (!formData.lastName || !formData.email || !formData.firstName || !formData.password) {
            notify("error", REQUIRED_ERROR);
            return null;
        }
        if (!checkValidEmail(formData.email)) {
            notify("error", ERROR_MESSAGE.VALID_EMAIL);
            return null;
        }
        if (formData.password !== formData.confPassword) {
            notify("error", ERROR_MESSAGE.PASSWORD_MATCH);
            return null;
        }
        setLoading(true);
        delete formData.confPassword;
        const res = await authService.signUpUser({ ...formData, ...formState });
        if (res && res.status) {
            const loginRes = await loginUser(dispatch, { password: formData.password, username: formData.email });
            if (loginRes && loginRes.status) {
                notify("success", SUCCESS_MESSAGE.LOGIN_SUCCESS);
                if (location.pathname === '/main') {
                    history.push('/home');
                } else if (location.pathname.includes('/course/')) {
                    history.push(location.pathname.replace('course', 'checkout'));
                }
            } else {
                notify("error", ERROR_MESSAGE.INVALID_CRED);
            }
        } else {
            notify("error", "Something went wrong!");
        }
        setLoading(false);
    };

    return (
        <div>
            <Dialog
                open={open}
                TransitionComponent={Transition}
                keepMounted
                fullWidth={true}
                maxWidth={'sm'}
                onClose={handleClose}
                aria-labelledby="alert-dialog-slide-title"
                aria-describedby="alert-dialog-slide-description"
            >
                <DialogTitle id="alert-dialog-slide-title" style={{ textAlign: 'center', position: 'relative' }}>
                    <Typography style={{ lineHeight: 2.5 }} variant="h1">
                        Sign Up
                    </Typography>
                    <Typography variant="subtitle1">
                        Learn on your own time from top universities and businesses.
                    </Typography>
                    <IconButton className={classes.closeBtn} onClick={handleClose}><CloseIcon /></IconButton>
                </DialogTitle>
                <DialogContent style={{ padding: '26px 32px' }}>
                    <Box component="form" id='signupForm' onSubmit={handleSubmit} className={classes.formBox} >
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={12} lg={6}>
                                <FormLabel className={classes.formLabel}>First Name *</FormLabel>
                                <Input
                                    fullWidth
                                    disableUnderline
                                    id="first_name"
                                    placeholder="Enter your First name"
                                    name="first_name"
                                    className={classes.inputStyle}
                                />
                            </Grid>
                            <Grid item xs={12} sm={12} lg={6}>
                                <FormLabel className={classes.formLabel}>Last Name *</FormLabel>
                                <Input
                                    className={classes.inputStyle}
                                    disableUnderline
                                    fullWidth
                                    id="last_name"
                                    placeholder="Enter your Last name"
                                    name="last_name"
                                />
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={12} lg={6}>
                                <FormLabel className={classes.formLabel}>Email *</FormLabel>
                                <Input
                                    id="email"
                                    className={classes.inputStyle}
                                    disableUnderline
                                    placeholder="Enter your Email address"
                                    name="email"
                                    fullWidth
                                />
                            </Grid>
                            <Grid item xs={12} sm={12} lg={6}>
                                <FormLabel className={classes.formLabel}>Phone Number *</FormLabel>
                                <PhoneInputField
                                    handleChange={(newVal) => setFormState({ ...formState, contact_no: newVal })}
                                    value={(formState.contact_no) ? formState.contact_no?.toString() : ""}
                                    customClass={classes.phoneInputStyle}
                                    name="contact_no"
                                />
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={12} lg={6}>
                                <FormLabel className={classes.formLabel}>Industry</FormLabel>
                                <Autocomplete
                                    id="combo-box-demo4"
                                    val={formState.industry}
                                    options={Industries}
                                    getOptionLabel={option => option}
                                    placeholder={"Select Industry"}
                                    handleChange={(event, newValue) => setFormState({ ...formState, industry: newValue })}
                                    styleOverrides={{ minWidth: 180, width: "100%", marginTop: 8 }}
                                    customClass={classes.occupationDropDown}
                                />
                            </Grid>
                            <Grid item xs={12} sm={12} lg={6}>
                                <FormLabel className={classes.formLabel}>Marketing Preferences</FormLabel>
                                <OutlinedSelect
                                    val={formState.marketing_preferences}
                                    options={marketingPrefOpt}
                                    selectStyle={{ minWidth: 180, marginTop: 0, padding: 4 }}
                                    styleOverrides={{ minWidth: '100%', marginBottom: 6, marginTop: 8 }}
                                    name="marketing_preferences"
                                    color={'secondary'}
                                    handleChange={handleInputChange}
                                />
                            </Grid>
                            <Grid item xs={12} sm={12} lg={6}>
                                <FormLabel className={classes.formLabel}>Referral Source</FormLabel>
                                <OutlinedSelect
                                    val={formState.referral_source}
                                    options={referralSourcesOpt}
                                    selectStyle={{ minWidth: 180, marginTop: 0, padding: 4 }}
                                    name="referral_source"
                                    styleOverrides={{ minWidth: '100%', marginBottom: 6, marginTop: 8 }}
                                    color={'secondary'}
                                    handleChange={handleInputChange}
                                />
                            </Grid>
                            <Grid item xs={12} sm={12} lg={6}>
                                <FormLabel className={classes.formLabel}>Occupation</FormLabel>
                                <Autocomplete
                                    id="combo-box-demo4"
                                    val={formState.occupation}
                                    options={OCCUPATIONS}
                                    onChange={(event, newValue) => {
                                        handleInputChange({ target: { name: 'occupation', value: newValue } });
                                    }}
                                    placeholder={"Select Occupation"}
                                    styleOverrides={{ minWidth: 180, width: "100%", marginTop: 8 }}
                                    customClass={classes.occupationDropDown}
                                />
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={12} lg={6}>
                                <FormLabel className={classes.formLabel}>Password</FormLabel>
                                <div style={{ position: "relative" }}>
                                    <Input
                                        fullWidth
                                        name="password"
                                        placeholder="Enter your password"
                                        type={showPassword ? 'text' : 'password'}
                                        id="password"
                                        className={classes.inputStyle}
                                        disableUnderline
                                        endAdornment={
                                            <InputAdornment position="end">
                                                <IconButton
                                                    aria-label="toggle password visibility"
                                                    onClick={() => setShowPassword(!showPassword)}>
                                                    {showPassword ? <Visibility /> : <VisibilityOff />}
                                                </IconButton>
                                            </InputAdornment>
                                        }
                                        style={{ marginBottom: 8 }}
                                    />
                                </div>
                            </Grid>
                            <Grid item xs={12} sm={12} lg={6}>
                                <FormLabel className={classes.formLabel}>Confirm Password</FormLabel>
                                <div style={{ position: "relative" }}>
                                    <Input
                                        fullWidth
                                        name="conf_password"
                                        placeholder="Enter your Confirm Password"
                                        type={showPassword ? 'text' : 'password'}
                                        id="conf_password"
                                        className={classes.inputStyle}
                                        disableUnderline
                                        style={{ marginBottom: 8 }}
                                    />
                                </div>
                            </Grid>
                        </Grid>
                        <Grid container style={{ marginTop: 28 }}>
                            <Button
                                type="submit"
                                fullWidth
                                variant="contained"
                                color="primary"
                                className={classes.loginBtn}
                                disabled={loading}
                            >
                                {loading ? <CircularProgress size={20} /> : "Sign Up"}
                            </Button>
                            <div className={classes.orLines}>
                                <span className={classes.line1}></span>
                                <span className={classes.orText}> or </span>
                                <span className={classes.line2}></span>
                            </div>
                            <Grid item xs={12} style={{ textAlign: "center", marginTop: 20, marginBottom: 20 }}>
                                <Typography component="span" variant="subtitle1">Already have account? <Button color="primary" onClick={handleOpenLogin} >Sign In</Button> </Typography>
                            </Grid>
                        </Grid>
                    </Box>
                </DialogContent>
            </Dialog>
        </div>
    );
}

export default SignUpDialogBox;