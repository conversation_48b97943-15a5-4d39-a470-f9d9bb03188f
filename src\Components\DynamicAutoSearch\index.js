import React from 'react';
import TextField from '@material-ui/core/TextField';
import Autocomplete from '@material-ui/lab/Autocomplete';
import CircularProgress from '@material-ui/core/CircularProgress';
import '../CustamizedFilters/filterModule.css';
import { SearchOutlined } from '@material-ui/icons';
import { InputAdornment } from '@material-ui/core';

export default function DynamicAutoSearch({ pageType, open, options, handleOpen, handleSearch }) {

  const loading = open && options.length === 0;
  
  return (
    <Autocomplete
      id="asynchronous-demo"
      style={{ width: 300 }}
      open={open}
      onOpen={() => {
        handleOpen(true);
      }}
      onClose={() => {
        handleOpen(false);
      }}
      onChange={(evt, newVal) => {
        window.location.href=`/course/${newVal.uuid}`;
      }}
      getOptionSelected={(option, value) => option.name === value.name}
      getOptionLabel={(option) => option.name}
      options={options}
      loading={loading}
      renderInput={(params) => (
        <TextField
          {...params}
          label={(pageType !== 'header') ? 'Search' : null}
          variant="standard"
          onChange={ev => {
            handleSearch(ev.target.value);
          }}
          className={(pageType !== 'header') ? "customInputBox" : "headerSearchBox"}
          InputProps={{
            ...params.InputProps,
            disableUnderline: true,
            startAdornment: (pageType === 'header') ? (<InputAdornment position="start"><SearchOutlined /> </InputAdornment>) : false,
            endAdornment: (
              <React.Fragment>
                {loading ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
              </React.Fragment>
            ),
          }}
        />
      )}
    />
  );
}