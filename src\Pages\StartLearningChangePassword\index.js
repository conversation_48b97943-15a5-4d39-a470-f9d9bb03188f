import React, { useEffect } from 'react';
import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';
import { CardContent, Container, Typography } from '@material-ui/core';
import Card from '@material-ui/core/Card';
import Input from '@material-ui/core/Input';
import Button from '@material-ui/core/Button';
import CircularProgress from '@material-ui/core/CircularProgress';
import { InputAdornment } from '@material-ui/core';
import VisibilityOffIcon from '@material-ui/icons/VisibilityOff';
import VisibilityIcon from '@material-ui/icons/Visibility';

import variables from '../../Sys/variable.scss';
import { UserServices } from 'Services/UserServices';
import { REQUIRED_ERROR } from 'utils/constants';
import { notify } from '../../utils';
import { logout, useAuthDispatch } from 'Context';
import { Paths } from 'Routes/routePaths';
import { useHistory } from 'react-router';

const useStyles = makeStyles(() => ({
  content: {
    minHeight: 305,
    maxWidth: 1040,
    marginTop: 20
  },
  inputStyle: {
    borderColor: '#fff',
    background: '#fff',
    borderRadius: '25px',
    color: '#989898',
    padding: '10px 15px'
  },
  pageHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    margin: '25px 0'
  },
  btn: {
    padding: '13px 35px',
    fontSize: '13px',
    color: 'white',
    borderRadius: '5px',
    marginLeft: '15px'
  },
  placeBtn: {
    display: 'flex',
    justifyContent: 'end',
    padding: '20px 0',
    marginTop: 20
  }
}));

const userServices = new UserServices();

const initFormState = { old_password: "", new_password: "", confirm_password: "" };

const StartLearningChangePassword = () => {
  const classes = useStyles();
  const history = useHistory();
  const dispatch = useAuthDispatch();

  const [loading, setLoading] = React.useState(false);
  const [userDetails, setUserDetails] = React.useState(null);
  const [showOldText, setShowOldText] = React.useState(false);
  const [showNewText, setShowNewText] = React.useState(false);
  const [showConfirmText, setShowConfirmText] = React.useState(false);
  const [formState, setFormState] = React.useState({ ...initFormState });

  useEffect(() => {
    getUserProfile();
  }, []);

  async function getUserProfile() {
    const user = await userServices.getDetailMyProfile();
    if (user && user.data && user.data.length) {
      setUserDetails(user.data[0]);
    }
  }

  const handleItemClick = async (path) => {
    if (path === 'logout') {
      await logout(dispatch);
      window.location.href = Paths.MainPage;
    } else {
      history.push({ pathname: path });
    }
  };

  const handleInputChange = (evt) => {
    const { name, value } = evt.target;
    setFormState({ ...formState, [name]: value });
  };

  const formValidationCheck = (e) => {
    e.preventDefault(); // ✅ stops search nav from triggering
    if (!formState.old_password || !formState.new_password || !formState.confirm_password) {
      notify("error", REQUIRED_ERROR);
      return null;
    }
    if (formState.new_password.length < 8 || formState.confirm_password.length < 8) {
      notify("error", 'New Password and Confirm Password must be 8 characters long');
      return null;
    }
    if (formState.new_password !== formState.confirm_password) {
      notify("error", 'New Password and Confirm Password must be same');
      return null;
    }
    return handleFormSubmit(formState);
  };

  const handleFormSubmit = async (formState) => {
    setLoading(true);
    await userServices.changePassword(formState).then(
      (data) => {
        if (data && data.status) {
          setLoading(false);
          notify("success", data.message);
          setFormState({ ...initFormState });
        }
      },
      (error) => {
        notify("error", error.response?.data?.message || error.message);
        setLoading(false);
      }
    );
  };

  const handleIconClick = (type) => {
    if (type === "old") setShowOldText(!showOldText);
    else if (type === "new") setShowNewText(!showNewText);
    else setShowConfirmText(!showConfirmText);
  };

  return (
    <main>
      <Container className={classes.content}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={12} lg={9}>
            <Card style={{ background: '#F7F9FA', marginBottom: 20, minHeight: 350 }}>
              <CardContent>
                <Typography
                  style={{
                    color: '#4D5766',
                    fontSize: 20,
                    fontWeight: 700,
                    lineHeight: '20px'
                  }}
                >
                  Change Password
                </Typography>

                <form onSubmit={formValidationCheck}>
                  <Grid container style={{ marginBottom: '15px', marginTop: '15px' }}>
                    {/* Old Password */}
                    <Grid item xs={12} sm={12} lg={6}>
                      <Typography
                        variant="subtitle2"
                        style={{ color: '#4D5766', marginBottom: '20px', marginTop: '15px' }}
                      >
                        Old Password
                      </Typography>
                      <Input
                        disableUnderline
                        value={formState.old_password}
                        type={showOldText ? 'text' : 'password'}
                        name="old_password"
                        autoComplete="new-password"
                        className={classes.inputStyle}
                        placeholder="Old Password"
                        style={{ minWidth: 220, width: '100%' }}
                        color="secondary"
                        onChange={handleInputChange}
                        endAdornment={
                          <InputAdornment
                            onClick={() => handleIconClick("old")}
                            position="end"
                            style={{ cursor: "pointer" }}
                          >
                            {showOldText ? <VisibilityIcon /> : <VisibilityOffIcon />}
                          </InputAdornment>
                        }
                      />
                    </Grid>

                    {/* New Password */}
                    <Grid item xs={12} sm={12} lg={6}>
                      <Typography
                        variant="subtitle2"
                        style={{ marginBottom: '20px', marginTop: '15px', color: variables.labelcolor }}
                      >
                        New Password
                      </Typography>
                      <Input
                        disableUnderline
                        value={formState.new_password}
                        type={showNewText ? 'text' : 'password'}
                        name="new_password"
                        autoComplete="new-password"
                        className={classes.inputStyle}
                        placeholder="New Password"
                        style={{ minWidth: 220, width: '100%' }}
                        color="secondary"
                        onChange={handleInputChange}
                        endAdornment={
                          <InputAdornment
                            onClick={() => handleIconClick("new")}
                            position="end"
                            style={{ cursor: "pointer" }}
                          >
                            {showNewText ? <VisibilityIcon /> : <VisibilityOffIcon />}
                          </InputAdornment>
                        }
                      />
                    </Grid>

                    {/* Confirm Password */}
                    <Grid item xs={12} sm={12} lg={6}>
                      <Typography
                        variant="subtitle2"
                        style={{ marginBottom: '20px', marginTop: '15px', color: variables.labelcolor }}
                      >
                        Confirm Password
                      </Typography>
                      <Input
                        disableUnderline
                        value={formState.confirm_password}
                        type={showConfirmText ? 'text' : 'password'}
                        name="confirm_password"
                        autoComplete="new-password"
                        placeholder="Confirm Password"
                        className={classes.inputStyle}
                        style={{ minWidth: 220, width: '100%' }}
                        color="secondary"
                        onChange={handleInputChange}
                        endAdornment={
                          <InputAdornment
                            onClick={() => handleIconClick("confirm")}
                            position="end"
                            style={{ cursor: "pointer" }}
                          >
                            {showConfirmText ? <VisibilityIcon /> : <VisibilityOffIcon />}
                          </InputAdornment>
                        }
                      />
                    </Grid>
                  </Grid>

                  {/* Submit Button */}
                  <div className={classes.placeBtn}>
                    <Button
                      color="primary"
                      variant="outlined"
                      className={classes.btn}
                      type="submit"
                    >
                      {loading ? <CircularProgress size={20} color="secondary" /> : 'Update'}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </main>
  );
};

export default StartLearningChangePassword;
