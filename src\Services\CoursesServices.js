import axios from 'axios';
import { getEncodeURL } from 'utils';
import AxiosInterCeptors from './AxiosInterCeptors.js';
import { PAYGATE_END_POINTS } from 'utils/constants.js';
import '../Sys/config.js';
export class CoursesServices extends AxiosInterCeptors {

    getAllCourses(url) {
        return axios.get(url)
            .then(res => res.data);
    }

    addCourses(payload, uuid) {
        if (uuid) {
            return axios.put(`main/coursedetail/${uuid}`, payload).then(res => res.data);
        }
        return axios.post("main/coursecreate", payload)
            .then(res => res.data)
    }

    getCourseDetail(id) {
        return axios.get(`main/coursedetail/${id}`)
            .then(res => res.data)
            .catch(err => (err.response && err.response.data) ? err.response.data : err);
    }

    getSessionLinkToCourse(course, user = null) {
        return axios.get(`main/gotocourse/${course}`)
            .then(res => res.data)
    }

    getCourseRequests(url) {
        return axios.get(url)
            .then(res => res.data)
    }

    updateCourseRequestStatus(id, payload) {
        return axios.patch(`main/courserequest/${id}/`, payload)
            .then(res => res.data)
            .catch(err => (err.response && err.response.data) ? err.response.data : err);
    }

    getEcommerceCourses(query = '') {
        let encodedURL = getEncodeURL(`project/coursefor_ecommerce?${query}`);
        return axios.get(encodedURL)
            .then(res => res.data)
    }

    getEcommerceCategories() {
        return axios.get(`project/course_categories`)
            .then(res => res.data)
    }

    async initPayGateTransaction(data) {
        return await axios.post('main/payment_callback', data)
            .then(res => res.data)
            .catch(err => (err.response && err.response.data) ? err.response.data : err);
    }
    // async initPayGateTransaction(data) {
    //     const CHECKSUM = generateMD5({...data}, ENCRIPTION_KEY);
    //     data = {...data, CHECKSUM };
    //     const formData = new FormData();
    //     Object.keys(data).forEach((key) => {
    //       formData.append(key, data[key]);
    //     });
    //     return await axios.post(PAYGATE_END_POINTS.INIT, formData, {
    //         headers: {
    //             "Content-type": "multipart/form-data"
    //         },
    //     })
    //     .then(res => res.data)
    //     .catch(err => (err.response && err.response.data) ? err.response.data : err);
    // }

    async getPayGateTransaction(data) {
        // const CHECKSUM = generateMD5({...data}, global.EncryptionKey);
        // data = {...data, CHECKSUM};
        const formData = new FormData();
        Object.keys(data).forEach((key) => {
            formData.append(key, data[key]);
        });
        return await axios.post(PAYGATE_END_POINTS.QUERY, formData, {
            headers: {
                "Content-type": "multipart/form-data"
            },
        })
            .then(res => res.data)
            .catch(err => (err.response && err.response.data) ? err.response.data : err);
    }

    getPaymentTransactionStatus(id) {
        return axios.get(`main/payment_status_check?transaction=${id}`)
            .then(res => res.data)
            .catch(err => (err.response && err.response.data) ? err.response.data : err);
    }

    getEcomCourseDetail(id, userId) {
        return axios.get(`main/coursedetailecom/${id}?userId=${userId}`)
            .then(res => res.data)
            .catch(err => (err.response && err.response.data) ? err.response.data : err);
    }

    saveOrderTransaction(payload) {
        return axios.post(`main/ordertransaction/`, payload)
            .then(res => res.data)
            .catch(err => (err.response && err.response.data) ? err.response.data : err);
    }

    getOrderTransections(query) {
        return axios.get(`main/ordertransaction/?` + query)
            .then(res => res.data)
            .catch(err => (err.response && err.response.data) ? err.response.data : err);
    }
}
