// ---- localStorage helpers ----
const storage = {
  set: (key, value) => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (e) {
      console.error("Error saving to localStorage", e);
    }
  },
  get: (key) => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (e) {
      console.error("Invalid JSON in localStorage for", key, e);
      return null;
    }
  },
  remove: (key) => {
    try {
      localStorage.removeItem(key);
    } catch (e) {
      console.error("Error removing from localStorage", e);
    }
  }
};

// ---- Get user from localStorage safely ----
let user = storage.get("currentUser");

let token = user?.token || "";
let SSOToken = user?.["IDP:SSOToken"] || "";

// ---- Initial state ----
export const initialState = {
  userDetails: user || "",
  token,
  SSOToken,
  loading: false,
  errorMessage: null,
};

// ---- Reducer ----
export const AuthReducer = (state, action) => {
  switch (action.type) {
    case "REQUEST_LOGIN":
      return {
        ...state,
        loading: true,
      };

    case "LOGIN_SUCCESS": {
      // Save user in localStorage
      storage.set("currentUser", action.payload);
      return {
        ...state,
        userDetails: action.payload,
        token: action.payload.token,
        SSOToken: action.payload["IDP:SSOToken"],
        loading: false,
      };
    }

    case "LOGOUT":
      storage.remove("currentUser");
      return {
        ...state,
        userDetails: "",
        token: "",
        SSOToken: "",
        loading: false,
      };

    case "LOGIN_ERROR":
      storage.remove("currentUser");
      return {
        ...state,
        userDetails: "",
        token: "",
        SSOToken: "",
        loading: false,
        errorMessage: action.error,
      };

    default:
      throw new Error(`Unhandled action type: ${action.type}`);
  }
};
