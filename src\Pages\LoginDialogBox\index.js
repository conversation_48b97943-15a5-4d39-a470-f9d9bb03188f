import React, { useEffect, useState } from 'react';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import Slide from '@material-ui/core/Slide';
import makeStyles from '@material-ui/core/styles/makeStyles';
import TextField from '@material-ui/core/TextField';
import Link from '@material-ui/core/Link';
import Grid from '@material-ui/core/Grid';
import Box from '@material-ui/core/Box';
import Typography from '@material-ui/core/Typography';
import FormLabel from '@material-ui/core/FormLabel';
import IconButton from '@material-ui/core/IconButton';
import { Visibility, VisibilityOff } from '@material-ui/icons';
import { notify } from '../../utils';
import { ERROR_MESSAGE, SUCCESS_MESSAGE } from '../../utils/constants';
import { loginUser, useAuthDispatch, useAuthState } from '../../Context';
import { CircularProgress } from '@material-ui/core';
import { useHistory, useLocation } from 'react-router-dom';
import CloseIcon from '@material-ui/icons/Close';

const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="left" ref={ref} {...props} />;
});

const useStyles = makeStyles(theme => ({
    formBox: {
        width: "100%",
        textAlign: "left"
    },
    formLabel: {
        fontWeight: "600",
        fontSize: 12,
        lineHeight: '15px',
        letterSpacing: "1px",
        textTransform: "uppercase",
        color: "#b1b1b1"
    },
    customTextField: {
        paddingTop: 15,
        paddingBottom: 15
    },
    input: {
        border: "1px solid #F0F1F7",
        borderRadius: 5,
        backgroundColor: "#FAFCFF",
        padding: 12
    },
    loginBtn: {
        padding: 18,
        fontSize: 14,
        fontWeight: 600,
        borderRadius: 5
    },
    eyeIconBtn: {
        position: "absolute",
        right: 10,
        top: 22,
        width: 25,
        height: 25,
        borderRadius: 15
    },
    closeBtn: {
        position: "absolute",
        top: 6,
        right: 6
    },
    orLines: {
        marginTop: 25,
        display: 'inline-block',
        position: 'relative',
        width: '100%'
    },
    line1: {
        position: 'absolute',
        borderTop: '1px solid #9FA2B4',
        width: 165,
        top: 0,
        left: 0
    },
    line2: {
        position: 'absolute',
        borderTop: '1px solid #9FA2B4',
        width: 155,
        top: 0,
        right: 0
    },
    orText: {
        position: 'absolute',
        top: '-10px',
        left: '50%',
        fontSize: 14,
        lineHeight: '20px'
    }
}));

const LoginDialogBox = ({ open, handleClose, handleOpenSignUp }) => {

    const classes = useStyles();
    const dispatch = useAuthDispatch();
    const history = useHistory();
    const { loading } = useAuthState();
    const location = useLocation();

    useEffect(() => {
        if (open) {
            document.getElementById("loginForm").reset();
        }
        //eslint-disable-next-line
    }, [open]);

    const [showPassword, setShowPassword] = useState(false);

    const handleSubmit = async (event) => {
        event.preventDefault();
        const data = new FormData(event.currentTarget);
        const formData = {
            username: data.get('username'),
            password: data.get('password'),
        };
        if (!formData.username || !formData.password) {
            notify("error", ERROR_MESSAGE.REQUIRED_LOGIN);
            return null;
        }
        const res = await loginUser(dispatch, formData);
        if (res && res.status) {
            notify("success", SUCCESS_MESSAGE.LOGIN_SUCCESS);
            if (location.pathname === '/main') {
                history.push('/home');
            } else if (location.pathname.includes('/course/')) {
                history.push(location.pathname.replace('course', 'checkout'));
            }
            return null;
        } else {
            notify("error", ERROR_MESSAGE.INVALID_CRED);
        }
    };

    return (
        <div>
            <Dialog
                open={open}
                style={{ borderRadius: 5 }}
                TransitionComponent={Transition}
                keepMounted
                fullWidth={true}
                maxWidth={'xs'}
                onClose={handleClose}
                aria-labelledby="alert-dialog-slide-title"
                aria-describedby="alert-dialog-slide-description"
            >
                <DialogTitle id="alert-dialog-slide-title" style={{ textAlign: 'center', position: 'relative' }}>
                    <Typography style={{ lineHeight: 2.5 }} variant="h1">
                        Welcome back
                    </Typography>
                    <Typography variant="subtitle1">
                        Accelerate your digital transformation ambitions.
                    </Typography>
                    <IconButton className={classes.closeBtn} onClick={handleClose}><CloseIcon /></IconButton>
                </DialogTitle>
                <DialogContent style={{ padding: '26px 32px' }}>
                    <Box component="form" id="loginForm" onSubmit={handleSubmit} className={classes.formBox} >
                        <FormLabel className={classes.formLabel}>Username</FormLabel>
                        <TextField
                            variant="outlined"
                            fullWidth
                            id="username"
                            placeholder="Enter your username"
                            name="username"
                            inputProps={{ className: classes.input }}
                            className={classes.customTextField}
                            style={{ marginBottom: 24 }}
                            autoComplete="username"
                            autoFocus
                        />
                        <FormLabel className={classes.formLabel}>Password</FormLabel>
                        <Link href="/forgot-password" variant="body2" style={{ textDecoration: "none", float: "right" }}>
                            <Typography style={{ fontSize: 13 }} component="span" variant="subtitle1">Forgot password? </Typography>
                        </Link>
                        <div style={{ position: "relative" }}>
                            <TextField
                                variant="outlined"
                                fullWidth
                                name="password"
                                placeholder="Enter your password"
                                type={showPassword ? 'text' : 'password'}
                                id="password"
                                inputProps={{ className: classes.input }}
                                className={classes.customTextField}
                                autoComplete="current-password"
                            />
                            <IconButton
                                aria-label="toggle password visibility"
                                className={classes.eyeIconBtn}
                                onClick={() => setShowPassword(!showPassword)}>
                                {showPassword ? <Visibility /> : <VisibilityOff />}
                            </IconButton>
                        </div>
                        <Grid container style={{ marginTop: 28 }}>
                            <Button
                                type="submit"
                                fullWidth
                                variant="contained"
                                color="primary"
                                className={classes.loginBtn}
                                disabled={loading}
                            >
                                {loading ? <CircularProgress size={20} /> : "Login"}
                            </Button>
                            <div className={classes.orLines}>
                                <span className={classes.line1}></span>
                                <span className={classes.orText}> or </span>
                                <span className={classes.line2}></span>
                            </div>
                            <Grid item xs={12} style={{ textAlign: "center", marginTop: 20, marginBottom: 20 }}>
                                <Typography component="span" variant="subtitle1">Don't have an account? <Button color="primary" onClick={handleOpenSignUp} >Sign Up</Button> </Typography>
                            </Grid>
                        </Grid>
                    </Box>
                </DialogContent>
            </Dialog>
        </div>
    );
}

export default LoginDialogBox;