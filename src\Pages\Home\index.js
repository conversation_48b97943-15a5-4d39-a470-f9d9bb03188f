import * as React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import { useLocation } from 'react-router';
import MainPage from '../MainPage';

export const useStyles = makeStyles(() => ({
    bannerWrap: {
        background: '#FAFCFF',
        display: "flex",
        width: "100%",
        marginBottom: 20
    },
    rgCourseTitle: {
        position: "absolute",
        top: "13px",
        left: "13px",
        color: "#FFFFFF",
        fontSize: "14px",
        fontWeight: "500",
    },
    content: {
        width: '100%',
        padding: 25,
    },
    mainTitle: {
        fontWeight: 700,
        fontSize: 40,
        paddingTop: 85
    },
    courseWrap: {
        // width: '100%',
        height: 300,
        position: 'relative',
        overflow: 'hidden',
        cursor: "pointer",
        display: 'flex',
        '&:hover .hoverColr': {
            display: 'block'
        },
        '&:hover img': {
            transform: 'scale(1.2)',
            top: -45
        },
        '&:hover .courseTitle': {
            top: 0,
            zIndex: 999,
            width: '100%'
        },
        '&:hover .courseDesc': {
            visibility: 'visible',
            opacity: 1
        },
    },
    courseImgWrap: {
        width: '100%',
    },
    overlayImg: {
        position: "absolute",
        top: 155,
        right: 0,
        maxWidth: '100%',
        transition: 'all 0.3s',
        display: 'block',
        width: '100%',
        height: 'auto',
        transform: 'scale(1)',
    },
    courseImg: {
        maxWidth: '100%',
        transition: 'all 0.3s',
        display: 'block',
        width: '100%',
        height: '100%',
        transform: 'scale(1)'
    },
    mainCrsImg: {
        maxWidth: '100%',
        height: 400,
        transition: 'all 0.3s',
        display: 'block',
        transform: 'scale(1)',
        width: '100%'
    },
    courseSection: {
        position: 'relative',
        overflow: 'hidden',
        cursor: 'pointer',
        borderRadius: 7,
        marginBottom: 10,
        '& .enrollBtn': {
            visibility: 'hidden',
            transition: 'all 300ms ease-in-out',
            position: 'absolute',
            bottom: 15,
            right: 30,
            padding: '15px 55px',
            borderRadius: 5
        },
        '&:hover img': {
            transform: 'scale(1.2)'
        },
        '&:hover .courseDesc': {
            visibility: 'visible',
            opacity: 1,
            top: 40,
            left: 14
        },
        '&:hover .enrollBtn': {
            visibility: 'visible',
            opacity: 1
        },
    },
    mainCrsImg1: {
        width: '100%',
        height: 196,
        maxWidth: '100%',
        transition: 'all 0.3s',
        display: 'block',
        transform: 'scale(1)'
    },
    overlayBgClr: {
        width: '100%',
        position: 'absolute',
        height: '100%',
        top: 0,
        background: 'linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 130, 202, 0) 100%)'
    },
    courseMainSec1: {
        width: '37%'
    },
    courseMainSec: {
        width: '26%'
    }
}));

const HomePage = () => {
    const location = useLocation();
    React.useEffect(() => {
        const href = window.location.href.substring(
          window.location.href.lastIndexOf('#') + 1
        );
        const element = document.getElementById(href);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
    }, [location]);
    
    return (
        <MainPage isHomePage={true} />
    );
}

export default HomePage;