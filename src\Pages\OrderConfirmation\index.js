import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import { Container, Typography, Button, Grid, CircularProgress } from '@material-ui/core';
import Layout from "Components/layouts";
import axios from 'axios';

const useStyles = makeStyles((theme) => ({
  root: {
    minHeight: '100vh',
    backgroundColor: '#f8f9fc',
    paddingTop: theme.spacing(8),
    paddingBottom: theme.spacing(8),
  },
  container: {
    maxWidth: 800,
    margin: '0 auto',
    textAlign: 'center',
  },
  loadingContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '60vh',
    gap: theme.spacing(2),
  },
  successTitle: {
    fontSize: '3rem',
    fontWeight: 700,
    color: '#1a1a1a',
    marginBottom: theme.spacing(3),
    [theme.breakpoints.down('sm')]: {
      fontSize: '2.5rem',
    },
  },
  successSubtitle: {
    fontSize: '1.2rem',
    color: '#666666',
    marginBottom: theme.spacing(6),
    fontWeight: 400,
    lineHeight: 1.5,
  },
  confirmationSection: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: theme.spacing(4),
    marginBottom: theme.spacing(4),
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
    border: '1px solid #e8e8e8',
  },
  confirmationTitle: {
    fontSize: '1.5rem',
    fontWeight: 600,
    color: '#cccccc',
    marginBottom: theme.spacing(4),
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
  },
  detailsGrid: {
    marginBottom: theme.spacing(3),
  },
  detailLabel: {
    fontSize: '1rem',
    color: '#999999',
    fontWeight: 500,
    marginBottom: theme.spacing(1),
    textAlign: 'left',
    [theme.breakpoints.down('sm')]: {
      textAlign: 'center',
    },
  },
  detailValue: {
    fontSize: '1rem',
    color: '#333333',
    fontWeight: 600,
    textAlign: 'left',
    [theme.breakpoints.down('sm')]: {
      textAlign: 'center',
      marginBottom: theme.spacing(2),
    },
  },
  launchButton: {
    backgroundColor: 'transparent',
    color: '#4A90E2',
    border: '2px solid #4A90E2',
    borderRadius: 25,
    padding: theme.spacing(1.5, 4),
    fontSize: '1rem',
    fontWeight: 600,
    textTransform: 'none',
    marginTop: theme.spacing(4),
    '&:hover': {
      backgroundColor: '#4A90E2',
      color: '#ffffff',
    },
    [theme.breakpoints.down('sm')]: {
      width: '100%',
      maxWidth: 300,
    },
  },
  detailRow: {
    marginBottom: theme.spacing(3),
    '&:last-child': {
      marginBottom: 0,
    },
  },
  subscriptionInfo: {
    backgroundColor: '#e8f4f8',
    borderRadius: 8,
    padding: theme.spacing(2),
    marginBottom: theme.spacing(3),
    border: '1px solid #b3d9e6',
  },
  subscriptionName: {
    fontSize: '1.1rem',
    fontWeight: 600,
    color: '#2c5282',
    marginBottom: theme.spacing(1),
  },
  subscriptionId: {
    fontSize: '0.9rem',
    color: '#718096',
    fontFamily: 'monospace',
  },
  errorContainer: {
    textAlign: 'center',
    padding: theme.spacing(4),
  },
  errorTitle: {
    fontSize: '1.5rem',
    fontWeight: 600,
    color: '#dc3545',
    marginBottom: theme.spacing(2),
  },
  errorMessage: {
    color: '#6c757d',
    marginBottom: theme.spacing(3),
  },
  retryButton: {
    backgroundColor: '#007bff',
    color: '#fff',
    '&:hover': {
      backgroundColor: '#0056b3',
    },
  },
}));

const SubscriptionSuccess = () => {
  const classes = useStyles();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [orderData, setOrderData] = useState(null);
  const [subscriptionData, setSubscriptionData] = useState(null);

  // Get UUID from localStorage
  const userId = localStorage.getItem('uuid') || '26469e57-9cfa-42dc-8ea3-32d80ecb0c74';

  useEffect(() => {
    fetchOrderSubscription();
  }, []);

  const fetchOrderSubscription = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching order subscription for user:', userId);

      const response = await axios.get(
        `https://api-staging.deviare.africa/main/orderSubscription/${userId}/`,
        {
          headers: {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-US,en;q=0.9',
            // 'authorization': `Token ${localStorage.getItem('token') || '518b8480834336686613d7e172389e80e35805c3'}`,
            'origin': window.location.origin,
            'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36'
          },
          timeout: 15000,
        }
      );

      console.log('Order subscription response:', response.data);

      if (response.data.success && response.data.data) {
        const data = response.data.data;
        setOrderData(data);
        
        if (data.confirmed_subscriptions && data.confirmed_subscriptions.length > 0) {
          setSubscriptionData(data.confirmed_subscriptions[0]);
        }
      } else {
        setError('Failed to load order information.');
      }
    } catch (error) {
      console.error('Error fetching order subscription:', error);
      setError('Failed to load order information. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleLaunchCourse = () => {
    // Navigate to courses page
    window.location.href = '/account/courses';
  };

  const handleRetry = () => {
    fetchOrderSubscription();
  };

  // Format current date for display
  const formatCurrentDate = () => {
    const now = new Date();
    return now.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Calculate next payment date (30 days from now)
  const getNextPaymentDate = () => {
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    return nextMonth.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Generate transaction number based on subscription ID
  const getTransactionNumber = () => {
    if (subscriptionData?.id) {
      return `#${subscriptionData.id.replace(/-/g, '').substring(0, 12).toUpperCase()}`;
    }
    return '#***********';
  };

  if (loading) {
    return (
      <Layout>
        <div className={classes.root}>
          <Container className={classes.container}>
            <div className={classes.loadingContainer}>
              <CircularProgress size={60} />
              <Typography variant="h6" color="textSecondary">
                Loading your subscription details...
              </Typography>
            </div>
          </Container>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className={classes.root}>
          <Container className={classes.container}>
            <div className={classes.errorContainer}>
              <Typography className={classes.errorTitle}>
                Unable to Load Order Information
              </Typography>
              <Typography className={classes.errorMessage}>
                {error}
              </Typography>
              <Button 
                variant="contained" 
                className={classes.retryButton}
                onClick={handleRetry}
              >
                Try Again
              </Button>
            </div>
          </Container>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className={classes.root}>
        <Container className={classes.container}>
          {/* Success Message */}
          <Typography className={classes.successTitle}>
            You're all set!
          </Typography>
          
          <Typography className={classes.successSubtitle}>
            Your subscription is active and you can now access all courses
          </Typography>

          {/* Active Subscription Info */}
          {subscriptionData && (
            <div className={classes.subscriptionInfo}>
              <Typography className={classes.subscriptionName}>
                {subscriptionData.display_name}
              </Typography>
              <Typography className={classes.subscriptionId}>
                Subscription ID: {subscriptionData.id}
              </Typography>
            </div>
          )}

          {/* Order Confirmation Card */}
          <div className={classes.confirmationSection}>
            <Typography className={classes.confirmationTitle}>
              Order Confirmation
            </Typography>

            <Grid container spacing={4} className={classes.detailsGrid}>
              {/* Transaction Number */}
              <Grid item xs={12} md={6} className={classes.detailRow}>
                <Typography className={classes.detailLabel}>
                  Transaction Number
                </Typography>
                <Typography className={classes.detailValue}>
                  {getTransactionNumber()}
                </Typography>
              </Grid>

              {/* Order Number */}
              <Grid item xs={12} md={6} className={classes.detailRow}>
                <Typography className={classes.detailLabel}>
                  Order Number
                </Typography>
                <Typography className={classes.detailValue}>
                  {getTransactionNumber()}
                </Typography>
              </Grid>

              {/* Amount */}
              <Grid item xs={12} md={6} className={classes.detailRow}>
                <Typography className={classes.detailLabel}>
                  Amount
                </Typography>
                <Typography className={classes.detailValue}>
                  R99/month
                </Typography>
              </Grid>

              {/* Payment Method */}
              <Grid item xs={12} md={6} className={classes.detailRow}>
                <Typography className={classes.detailLabel}>
                  Payment Method
                </Typography>
                <Typography className={classes.detailValue}>
                  Visa ending in 2194
                </Typography>
              </Grid>

              {/* Start Date */}
              <Grid item xs={12} md={6} className={classes.detailRow}>
                <Typography className={classes.detailLabel}>
                  Start Date
                </Typography>
                <Typography className={classes.detailValue}>
                  {formatCurrentDate()}
                </Typography>
              </Grid>

              {/* Next Payment */}
              <Grid item xs={12} md={6} className={classes.detailRow}>
                <Typography className={classes.detailLabel}>
                  Next Payment
                </Typography>
                <Typography className={classes.detailValue}>
                  {getNextPaymentDate()}
                </Typography>
              </Grid>
            </Grid>

            {/* Order Confirmation Message */}
            {orderData?.order_confirmation_message && (
              <Typography 
                variant="body2" 
                style={{ 
                  marginTop: 16, 
                  color: '#4caf50', 
                  fontWeight: 500,
                  fontStyle: 'italic'
                }}
              >
                {orderData.order_confirmation_message}
              </Typography>
            )}
          </div>

          {/* Launch Course Button */}
          <Button
            variant="outlined"
            className={classes.launchButton}
            onClick={handleLaunchCourse}
          >
            Launch Course
          </Button>
        </Container>
      </div>
    </Layout>
  );
};

export default SubscriptionSuccess;
