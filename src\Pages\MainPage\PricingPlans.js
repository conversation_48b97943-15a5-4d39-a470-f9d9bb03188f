import React, { useState, useEffect } from 'react';
import { Container, Grid, Typography, Button, Paper, Dialog, IconButton } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { useLocation } from 'react-router-dom/cjs/react-router-dom';
import CloseIcon from '@material-ui/icons/Close';

const useStyles = makeStyles((theme) => ({
  root: {
    margin: '40px 0',
    padding: '0 0 40px 0',
  },
  toggleWrap: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
    background: '#f2f2f2',
    borderRadius: 20,
    width: '1200px',
    margin: 'auto',
    padding: 4,
  },
  toggleBtn: {
    borderRadius: 20,
    padding: '12px 32px',
    fontWeight: 700,
    width:'1200px',
    fontSize: 16,
    background: 'transparent',
    color: '#888',
    border: 'none',
    outline: 'none',
    cursor: 'pointer',
    '&.active': {
      background: '#fff',
      color: '#222',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    },
  },
  planCard: {
    padding: 24,
    borderRadius: 12,
    boxShadow: '0 2px 8px #eee',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    height: '100%',
  },
  planTitle: {
    fontWeight: 700,
    fontSize: 24,
    marginTop: 16,
    marginBottom: 4,
    color: '#0d1b2a',
  },
  planPrice: {
    fontWeight: 600,
    fontSize: 16,
    marginBottom: 16,
    color: '#444',
  },
  enrollBtn: {
    marginTop: 'auto',
    background: 'primary',
    color: 'primary',
    borderRadius: 8,
    padding: '8px 24px',
    fontWeight: 600,
    textTransform: 'none',
    '&:hover': {
      background: '#22507c',
    },
  },
  includesTitle: {
    fontWeight: 700,
    fontSize: 14,
    marginBottom: 8,
    color: '#0d1b2a',
  },
  featureList: {
    listStyle: 'none',
    padding: 0,
    margin: 0,
    marginBottom: 16,
  },
  featureItem: {
    display: 'flex',
    alignItems: 'center',
    fontSize: 14,
    marginBottom: 4,
    color: '#222',
  },
  check: {
    color: '#0080CA',
    fontWeight: 700,
    marginRight: 8,
  },
  // New styles for iframe modal
  iframeDialog: {
    '& .MuiDialog-paper': {
      margin: 0,
      width: '100vw',
      height: '100vh',
      maxWidth: 'none',
      maxHeight: 'none',
      borderRadius: 0,
    },
  },
  iframeContainer: {
    position: 'relative',
    width: '100%',
    height: '100vh',
    display: 'flex',
    flexDirection: 'column',
  },
  iframeHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(1, 2),
    backgroundColor: '#f5f5f5',
    borderBottom: '1px solid #ddd',
    minHeight: 56,
  },
  iframeTitle: {
    fontWeight: 600,
    color: '#333',
  },
  closeButton: {
    color: '#666',
  },
  iframe: {
    flex: 1,
    width: '100%',
    border: 'none',
    backgroundColor: '#fff',
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    fontSize: '18px',
    color: '#666',
  },
}));

const PricingPlans = (props) => {
  const classes = useStyles();
  const [billing, setBilling] = useState('monthly');
  const [coursePlans, setCoursePlans] = useState(null);
  const [profileData, setProfileData] = useState(null);
  const [iframeOpen, setIframeOpen] = useState(false);
  const [iframeUrl, setIframeUrl] = useState('');
  const [iframeLoading, setIframeLoading] = useState(false);
  
  const query = new URLSearchParams(useLocation().search);
  const id = query.get("course_id");

  // Fetch user profile from API and set all fields
  useEffect(() => {
    fetch('https://api-staging.deviare.africa/main/myprofile', {
      method: 'GET',
      headers: {
        'accept': 'application/json, text/plain, */*',
         'authorization': `Token ${localStorage.getItem('token')}`,
      }
    })
      .then(res => res.json())
      .then(data => {
        if (data.status && data.data && data.data.length > 0) {
          setProfileData(data.data[0]);
        }
      })
      .catch(err => {
        console.error('Failed to fetch profile:', err);
      });
  }, []);

  useEffect(() => {
    if (id) {
      fetch(`https://api-staging.deviare.africa/main/coursedetails/${id}`)
        .then((res) => res.json())
        .then((data) => {
          if (data.status && data.data && data.data.pricing) {
            setCoursePlans({
              monthly: data.data.pricing.monthly_plan,
              annual: data.data.pricing.annual_plan,
              features: [
                ...(data.data.course_objectives ? [data.data.course_objectives] : []),
                ...(data.data.course_outline ? [data.data.course_outline] : []),
                ...(data.data.pricing.monthly_plan?.description ? [data.data.pricing.monthly_plan.description] : []),
              ],
            });
          }
        })
        .catch((error) => {
          console.error('Error fetching course details:', error);
        });
    }
  }, [id]);

  // Function to handle enrollment with iframe
  const handleEnrollment = (planType) => {
    if (!profileData) {
      alert('Please log in again to continue with enrollment.');
      return;
    }
    
    const firstName = profileData.firstName || 'User';
    const lastName = profileData.lastName || '';
    const email = profileData.email || '';
    
    if (!email) {
      alert('Please log in again to continue with enrollment.');
      return;
    }

    const subscriptionUrl = `https://deviare.subscriptionflow.com/en/hosted-page/subscribe/${coursePlans[planType].plan_id}/product/${id}?ai_firstName=${encodeURIComponent(firstName)}&ai_lastName=${encodeURIComponent(lastName)}&ai_email=${encodeURIComponent(email)}`;
    
    console.log('Opening subscription URL in iframe:', subscriptionUrl);
    setIframeUrl(subscriptionUrl);
    setIframeLoading(true);
    setIframeOpen(true);
  };

  // Handle iframe load
  const handleIframeLoad = () => {
    setIframeLoading(false);
  };

  // Handle closing iframe
  const handleCloseIframe = () => {
    setIframeOpen(false);
    setIframeUrl('');
    setIframeLoading(false);
  };

  // Listen for messages from iframe (optional - for subscription success)
  useEffect(() => {
    const handleMessage = (event) => {
      // Add your domain check for security
      if (event.origin !== 'https://deviare.subscriptionflow.com') {
        return;
      }
      
      // Handle subscription success/failure messages
      if (event.data && event.data.type === 'subscription_success') {
        console.log('Subscription successful!');
        handleCloseIframe();
        // You can redirect to success page or show success message
        // window.location.href = '/subscription-success';
      }
      
      if (event.data && event.data.type === 'subscription_cancelled') {
        console.log('Subscription cancelled');
        handleCloseIframe();
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // Original plans
  const originalPlans = [
    {
      name: 'Free',
      price: 0,
      features: ['Learning Objects'],
    },
    {
      name: 'Basic',
      price: 99,
      features: ['Learning Objects', 'Courses'],
    },
    {
      name: 'Plus',
      price: 159,
      features: ['Learning Objects', 'Courses', 'Curriculum'],
    },
    {
      name: 'Premium',
      price: 299,
      features: ['Learning Objects', 'Courses', 'Curriculum', 'Hands on Labs'],
    },
  ];

  // If course_id present and coursePlans loaded, show only monthly/annual API plans
  if (id && coursePlans) {
    return (
      <>
        <div className={classes.root}>
          <div className={classes.toggleWrap}>
            <button
              className={`${classes.toggleBtn} ${billing === 'monthly' ? 'active' : ''}`}
              onClick={() => setBilling('monthly')}
            >
              Monthly
            </button>
            <button
              className={`${classes.toggleBtn} ${billing === 'annual' ? 'active' : ''}`}
              onClick={() => setBilling('annual')}
            >
              Annual
            </button>
          </div>
          <Container maxWidth="lg">
            <Grid container spacing={2} justifyContent="center">
              {[billing].map((planType) => (
                <Grid item xs={12} sm={6} md={4} key={planType}>
                  <Paper className={classes.planCard} elevation={2}>
                    <div className={classes.includesTitle}>Includes</div>
                    <div className={classes.planTitle}>{coursePlans[planType]?.plan_name}</div>
                    <div className={classes.featureList}>{coursePlans[planType]?.description}</div>
                    <div className={classes.planPrice}>
                      {coursePlans[planType]?.total_amount} {planType === 'monthly' ? 'per month' : 'per year'}
                    </div>
                    <Button 
                      className={classes.enrollBtn} 
                      onClick={() => handleEnrollment(planType)}
                      variant="contained" 
                      color="primary"
                      disabled={!profileData || !profileData.email}
                    >
                      {profileData && profileData.email ? 'Enroll Now' : 'Login Required'}
                    </Button>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Container>
        </div>

        {/* Full Screen Iframe Dialog */}
        <Dialog
          open={iframeOpen}
          onClose={handleCloseIframe}
          className={classes.iframeDialog}
          fullScreen
        >
          <div className={classes.iframeContainer}>
            <div className={classes.iframeHeader}>
              <Typography className={classes.iframeTitle}>
                Complete Your Subscription
              </Typography>
              <IconButton 
                className={classes.closeButton}
                onClick={handleCloseIframe}
                aria-label="close"
              >
                <CloseIcon />
              </IconButton>
            </div>
            {iframeLoading && (
              <div className={classes.loadingContainer}>
                Loading subscription page...
              </div>
            )}
            {iframeUrl && (
              <iframe
                src={iframeUrl}
                className={classes.iframe}
                title="Subscription Page"
                onLoad={handleIframeLoad}
                allow="payment; camera; microphone"
                sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
              />
            )}
          </div>
        </Dialog>
      </>
    );
  }

  // Otherwise, show original plans
  return (
    <div className={classes.root}>
      <div className={classes.toggleWrap}>
        <button
          className={`${classes.toggleBtn} ${billing === 'monthly' ? 'active' : ''}`}
          onClick={() => setBilling('monthly')}
        >
          Monthly
        </button>
        <button
          className={`${classes.toggleBtn} ${billing === 'annual' ? 'active' : ''}`}
          onClick={() => setBilling('annual')}
        >
          Annual
        </button>
      </div>
      <Container maxWidth="lg">
        <Grid container spacing={2} justifyContent="center">
          {originalPlans.map((plan) => (
            <Grid item xs={12} sm={6} md={3} key={plan.name}>
              <Paper className={classes.planCard} elevation={2}>
                <div className={classes.includesTitle}>Includes</div>
                <ul className={classes.featureList}>
                  {plan.features.map((f) => (
                    <li className={classes.featureItem} key={f}>
                      <span className={classes.check}>✓</span> {f}
                    </li>
                  ))}
                </ul>
                <div className={classes.planTitle}>{plan.name}</div>
                <div className={classes.planPrice}>
                  {plan.price === 0
                    ? 'Free'
                    : billing === 'monthly'
                    ? `R${plan.price}.00 per month`
                    : `R${plan.price * 12}.00 per year`}
                </div>
                <Button className={classes.enrollBtn} variant="contained" color="primary">
                  Enroll Now
                </Button>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Container>
    </div>
  );
};

export default PricingPlans;
