import React, { useState } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
    AppB<PERSON>,
    Toolbar,
    Button,
    IconButton
} from '@material-ui/core';
import { NavLink } from 'react-router-dom';
import logoImg from 'Assets/images/logo.jpg';
import mobileLogo from 'Assets/images/deviare-minimum-logo.png';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import ExpandLessIcon from '@material-ui/icons/ExpandLess';
import { Paths } from 'Routes/routePaths'
import { useHistory, useLocation } from 'react-router';
import variables from 'Sys/variable.scss';
import { CoursesServices } from 'Services/CoursesServices';
import DynamicAutoSearch from 'Components/DynamicAutoSearch';
import { IsMobileDevice, getEncodeURL } from 'utils';
import CategoryDrawer from 'Components/CategoryDrawer';
import DehazeIcon from '@material-ui/icons/Dehaze';
import { BrowseCoursePanel } from 'Components/layouts/Header';
import MenuBookIcon from '@material-ui/icons/MenuBook';

const coursesService = new CoursesServices();

const useStyles = makeStyles((theme) => ({
    root: {
        flexGrow: 1,
    },
    headerBar: {
        backgroundColor: variables.headerColor,
        height: 95,
        boxShadow: "none"
    },
    menuButton: {
        marginRight: theme.spacing(2),
    },
    title: {
        flexGrow: 1,
    },
    searchBar: {
        flexGrow: 1,
        marginLeft: 20,
        display: 'inline-flex',
        alignItems: 'center',
        gap: 10,
        marginTop: 20
    },
    iconBtn: {
        borderRadius: "50%"
    },
    profileImg: {
        width: 80,
        height: 80,
        margin: "0 auto",
        overflow: "hidden",
        border: "1px solid",
        borderRadius: "50%",
        backgroundColor: "#262626"
    },
    editBack: {
        position: "absolute",
        width: 75,
        height: 26,
        bottom: 0,
        textAlign: "center",
        paddingTop: 6,
        color: "#fff",
        background: "rgba(255, 255, 255, 0.6)"
    },
    rightMenuItem: {
        width: '100%',
        position: 'relative',
        textAlign: 'center',
        margin: 'auto',
        '&:hover': {
            color: '#3C7DDD'
        }
    },
    mainMenuItem: {
        width: '50%',
        position: 'relative',
        marginLeft: '35px',
        marginTop: 20,
        '&:hover': {
            color: '#3C7DDD'
        }
    },
    menuContainer: {
        display: 'grid',
        gridTemplateColumns: 'auto auto auto auto',
        backgroundColor: 'red',
    },
    menuContainerItem: {
        margin: 15,
    },
    header: {
        fontSize: '16px',
        fontStyle: 'bold',
        fontWeight: 800,
    },
    menuSubContainer: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
    },
    viewAll: {
        color: '#3C7DDD',
        cursor: 'pointer',
    },
    lastRow: {
        gridColumn: 'span 4',
        justifySelf: 'end',
    },
    browseAll: {
        color: '#3C7DDD',
        background: 'white',
        cursor: 'pointer',
        padding: '20px 45px',
        marginTop: 40,
    },
    exploreIconBtn: {
        position: 'absolute',
        right: 0,
        top: -5
    }
}));

export const TopBarButton = ({
    name,
    icon,
    layout,
    path,
    button = false,
    activeClassName = 'active'
}) => (
    <NavLink
        to={layout + path}
        activeClassName={activeClassName}
        exact
        id="topBar_nav">
        <Button
            color={button ? 'primary' : 'secondary'}
            variant={button ? 'contained' : 'default'}
            size="large"
            id="topBar_button">
            <span id="topBar_span" ><i className={icon} /></span>
            <span> {name} </span>
        </Button>
    </NavLink>
);

const ExHeader = ({ handleOpenModal }) => {
    const [dropdownOpen, setDropdownOpen] = useState(false);

    const classes = useStyles();
    const history = useHistory();
    const location = useLocation();

    const anchorEl = React.useRef(null);
    const [menuOpen, setMenuOpen] = useState(false);
    const [courseCatalogueList, setCourseCatalogueList] = useState([]);
    const [coursesDropdown, setCoursesDropdown] = useState([]);
    const [searchText, setSearchText] = useState("");
    const [openCatDrawer, setOpenCatDrawer] = useState(false);
    const [courseLoading, setCourseLoading] = useState(false);


    const [categoryList, setCategoryList] = useState([]);
    const [selectedCategory, setSelectedCategory] = useState(null);


    const isMobile = IsMobileDevice('sm');
    const menu_items = [
        { name: "Courses", path: Paths.CourseListing },
        { name: "Plan", path: Paths.Accreditations },
        // { name: "Contact", path: `${Paths.MainPage}/#contactsec` }
    ];

    React.useEffect(() => {
        getCategoriesList();
        fetchAllCoursesDropdown();
        // eslint-disable-next-line
    }, []);

    const fetchAllCoursesDropdown = async () => {
        try {
            const response = await fetch('https://api-staging.deviare.africa/main/coursedropdownsandlist');
            const data = await response.json();
            setCoursesDropdown(data?.data?.courses || []);
        } catch (err) {
            setCoursesDropdown([]);
        }
    };

    // Search courses for dropdown
    const handleSearchInputChange = async (e) => {
    setDropdownOpen(true);
        const value = e.target.value;
        setSearchText(value);
        if (!value.trim()) {
            fetchAllCoursesDropdown();
            return;
        }
        try {
            const response = await fetch(`https://api-staging.deviare.africa/main/coursedropdownsandlist?search=${encodeURIComponent(value)}`);
            const data = await response.json();
            setCoursesDropdown(data?.data?.courses || []);
        } catch (err) {
            setCoursesDropdown([]);
        }
    };

    const handleClose = (event) => {
        if (anchorEl.current && anchorEl.current.contains(event.target)) {
            return;
        }
        setMenuOpen(false);
    };

    const getCourseCatalogue = async (category) => {
        let query = '';
        query += category ? `category=${category}&popular=true` : 'popular=true';
        setCourseLoading(true);
        const courseList = await coursesService.getEcommerceCourses(query);
        const courses = [];
        if (courseList.status) {
            courseList?.data?.map((cat) => {
                return cat?.courses?.map((itm) => {
                    courses.push({ ...itm });
                    return itm;
                });
            });
        }
        setCourseCatalogueList(courses);
        setCourseLoading(false);
    }

    const getCategoriesList = async () => {
        const categories = await coursesService.getEcommerceCategories();
        if (categories?.status) {
            setCategoryList(categories?.data?.categories || []);
            // const selected = categories?.data?.categories[0]; 
            // setSelectedCategory(selected);
            return getCourseCatalogue();
        }
    }

    const handleToggleMenu = () => {
        setMenuOpen((prevOpen) => !prevOpen);
    };

    const toggleCategoryDrawer = () => {
        setOpenCatDrawer((prevOpen) => !prevOpen);
    };

    const getSearchCourseList = async (searchVal) => {
        const courseList = await coursesService.getECommerceCourses(searchVal ? `name=${searchVal}` : '');
    // This function is now unused and can be removed, as setSearchCourses is not defined
    }

    const handleGlobalSearch = (value) => {
        getSearchCourseList(value);
    };

    const handleSubManuClicked = (course) => {
        setMenuOpen(false);
        history.push((course?.uuid) ? `/course/${course?.uuid}?course_id=${course?.uuid}` : '/course-listing');
    }

    const handleCategoryClick = (category) => {
        if (selectedCategory === category) {
            return null;
        }
        setSelectedCategory(category);
        return getCourseCatalogue(category);
    }

    return (
        <div className={classes.root}>
            <AppBar
                position="static"
                className={classes.headerBar}
            >
                <Toolbar>
                    <div style={{ margin: 'auto' }}>
                        {(isMobile) ?
                            <img
                                style={{ cursor: "pointer", paddingTop: 8, width: 160, maxHeight: 85 }}
                                alt="Deviare"
                                onClick={() => history.push(Paths.HomePage)}
                                src={logoImg}
                            /> :
                            <img
                                style={{ cursor: "pointer", paddingTop: 8, width: 60, maxHeight: 80 }}
                                alt="Deviare"
                                onClick={() => history.push(Paths.HomePage)}
                                src={mobileLogo}
                            />
                        }
                    </div>
                    {(isMobile) &&
                        <div className={classes.mainMenuItem}>
                           {/* <Button
    style={{ color: (location.pathname.startsWith(Paths.CourseListing)) ? '#0080CA' : '#4D5766', fontSize: 16 }}
    ref={anchorEl}
    aria-controls={menuOpen ? 'menu-list-grow' : undefined}
    aria-haspopup="true"
    onClick={handleToggleMenu}
>
    Courses 
</Button> */}

                            {menu_items.map((menuItem) => (
                                <Button
                                    style={{ color: (location.pathname.startsWith(menuItem.path)) ? '#0080CA' : '#4D5766', fontSize: 16 }}
                                    onClick={() => history.push(menuItem.path)}
                                >
                                    {menuItem.name}
                                </Button>
                            ))}
                        </div>}
                    <div className={classes.rightMenuItem}>
                        <div className={classes.searchBar}>
                            {(isMobile) && (
                                <div style={{ position: 'relative', width: 200 }}>
                                    <input
                                        type="text"
                                        value={searchText}
                                        onChange={handleSearchInputChange}
                                        onFocus={() => setDropdownOpen(true)}
                                        onBlur={() => setTimeout(() => setDropdownOpen(false), 150)}
                                        placeholder="Search courses..."
                                        style={{ padding: '10px', borderRadius: 4, border: '1px solid #ccc', width: '100%' }}
                                    />
                                    {(dropdownOpen && coursesDropdown.length > 0) && (
                                        <div style={{ background: '#fff', border: '1px solid #eee', marginTop: 2, borderRadius: 4, maxHeight: 300, overflowY: 'auto', width: '100%', position: 'absolute', zIndex: 10 }}>
                                            {coursesDropdown.map((course) => (
                                                <div key={course.uuid} style={{ padding: '10px', borderBottom: '1px solid #eee', cursor: 'pointer' }}
                                                    onMouseDown={() => { setDropdownOpen(false); history.push(`/course/${course?.uuid}?course_id=${course?.uuid}`); }}>
                                                    {course.name}
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            )}
                           
                            {(isMobile) && <Button
                               variant="outlined"
                                // color="primary"
                                aria-haspopup="true"
                                     style={{ padding: '14px 45px', borderRadius: 5 ,borderBlockStartStyle: '#3D7DDD'}}
                                onClick={() => {
                                    window.location.href =
'https://identity-staging.deviare.africa/auth/realms/Deviare/protocol/openid-connect/registrations' +
            '?client_id=e-commerce' +
            '&response_type=code' +
            '&scope=openid' +
           `&redirect_uri=https://learn-dev.deviareacademy.africa/profile`;
                                }}
                                >
                                Sign Up
                            </Button>
                            }
                             <Button
                              variant="outlined"
                                color="primary"
                                aria-haspopup="true"
                                // style={{ marginRight: 20, color: '#3D7DDD' }}
                                style={{ padding: '14px 45px', borderRadius: 5 }}
                                onClick={() => handleOpenModal('login')}
                            >
                                Login
                            </Button>
                            {(!isMobile) &&
                                <IconButton className={classes.exploreIconBtn} onClick={toggleCategoryDrawer}>
                                    <DehazeIcon color="primary" />
                                </IconButton>}
                        </div>
                    </div>
                </Toolbar>
            </AppBar>
            {(isMobile) && <BrowseCoursePanel
                menuOpen={menuOpen}
                courseLoading={courseLoading}
                courseList={courseCatalogueList}
                handleSubManuClicked={handleSubManuClicked}
                handleCategoryClick={handleCategoryClick}
                selectedCategory={selectedCategory}
                categoryList={categoryList}
                handleCourseMenuClose={handleClose}
                menuRef={anchorEl}
            />}
            {(!isMobile) && <>
                <CategoryDrawer
                    open={openCatDrawer}
                    isAuth={false}
                    handleViewAllClick={(cat) => {
                        history.push(getEncodeURL(`${Paths.CourseListing}?category=${cat}`))
                    }}
                    list={categoryList}
                    toggleDrawer={toggleCategoryDrawer}
                />
            </>}
        </div>
    );
};

export default ExHeader;