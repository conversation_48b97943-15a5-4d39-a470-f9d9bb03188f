import "../Sys/config.js";

export const authService = {
  login,
  forgotPassword,
  resetpassword,
  gettheme,
  adminlogin,
  ssologin,
  callback,
  signUpUser,
  authorizeafterlogin
};

const apiUrl = global.platformURI;
//'https://calendar-api.deviare.africa/';

const cookieUrl = process.env.REACT_APP_KEYCLOAK_COOKIE_ENDPOINT;
const relayUrl = process.env.REACT_APP_KEYCLOAK_RELAY;
function login(username, password) {
  const requestOptions = {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password })
  };

  return fetch(`${apiUrl}main/authorize`, requestOptions)
    .then(handleResponse)
    .then(user => {
      return user;
    });
}
function authorizeafterlogin(sso_token) {
  const requestOptions = {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ sso_token })
  };

  return fetch(`${apiUrl}main/authorize`, requestOptions)
    .then(handleResponse)
    .then(user => {
      return user;
    });
}
function adminlogin(username, password) {
  const requestOptions = {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password })
  };

  return fetch(`${apiUrl}main/authorize`, requestOptions)
    .then(handleResponse)
    .then(user => {
      return user;
    });
}

function gettheme(branch_url) {
  return fetch(`${apiUrl}main/customertheme?branch_url=` + branch_url)
    .then(handleResponse)
    .then(user => {
      return user;
    });
}

function callback(code, state, courseid) {
  const requestOptions = {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      code: code,
      state: state,
      courseid: courseid
    })
  };
  return fetch(`${apiUrl}main/authprovider`, requestOptions)
    .then(handleResponse)
    .then(user => {
      return user;
    });
}

function ssologin(getprams) {
  let url = `${apiUrl}main/authprovider`;
  if (getprams) {
    url = url + '?' + getprams;
  }

  return fetch(url)
    .then(handleResponse)
    .then(user => {
      return user;
    });
}

function forgotPassword(username) {
  const requestOptions = {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email: username })
  };
  return fetch(`${apiUrl}main/forgetpassword`, requestOptions)
    .then(handleResponse)
    .then(user => {
      return user;
    })
    .catch(err => (err.response && err.response.data) ? err.response.data : err);
}

function resetpassword(userName, token, newPassword) {
  const requestOptions = {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      username: userName,
      token: token,
      password: newPassword
    })
  };
  return fetch(`${apiUrl}main/resetpassword`, requestOptions)
    .then(handleResponse)
    .then(user => {
      return user;
    });
}

function handleResponse(response) {
  return response.text().then(text => {
    const data = text && JSON.parse(text);
    if (!response.ok) {
      if (response.status === 401) {
        // auto logout if 401 response returned from api
        // logout();
      }

      const error = data || response.statusText;
      return Promise.reject(error);
    }

    return data;
  });
}

function signUpUser(body) {
  const requestOptions = {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(body)
  };
  return fetch(`${apiUrl}main/usersignup`, requestOptions)
    .then(handleResponse)
    .then(user => {
      return user;
    });
}

export const handleSSOAuth = (courseUrl, client = 'simplilearn') => {
  let token = localStorage.getItem('ssoToken');
  let ssoToken = { url_search_string: localStorage.getItem('SSO') };
  try {
    ssoToken = JSON.parse(atob(token));
  } catch (error) {
    ssoToken = { url_search_string: localStorage.getItem('SSO') };
  }
  if (!ssoToken || !courseUrl) {
    return;
  }
  try {
    let relay = `${relayUrl.replace('{realm_client}', client)}${courseUrl}`;
    let redirectUrl = `${cookieUrl}${ssoToken.url_search_string}${encodeURI(
      relay
    )}`;
    window.open(redirectUrl);
  } catch (error) {
    alert(error);
  }
};

export async function authorizeWithSSO(sso_token) {
  try {
    const response = await fetch(`https://api-staging.deviare.africa/main/authorize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ sso_token }),
    });

    if (!response.ok) {
      throw new Error('Failed to authorize user');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Authorization error:', error);
    throw error;
  }
}


window.handleSSOAuth = handleSSOAuth;
window.cookieUrl = cookieUrl;
window.relayUrl = relayUrl;
