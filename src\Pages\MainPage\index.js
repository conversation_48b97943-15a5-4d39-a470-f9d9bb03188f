import * as React from 'react';
import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';
import courseImg1 from 'Assets/images/devImg.png';
import courseImg2 from 'Assets/images/homePage/contactUs.png';
import courseImg3 from 'Assets/images/MSAzDeveloper.png';
import courseImg4 from 'Assets/images/medium-shot-woman.png';
import sliderImg from 'Assets/images/homePage/Header.png';
import { Button, Checkbox, Chip, CircularProgress, Container, FormControlLabel, Input, Typography } from '@material-ui/core';
import Layout from 'Components/layouts';
import Carousel from 'Components/Carousel';
import csImg from 'Assets/images/customer-default-logo.png';
import { IsMobileDevice, getCourseImage, notify } from 'utils';
import CustomTabs from 'Components/CustomTabs';
import OutlinedSelect from 'Components/OutlinedSelect';
import { useHistory } from 'react-router';
import Card from '@material-ui/core/Card';
import IconButton from '@material-ui/core/IconButton';
import CardContent from '@material-ui/core/CardContent';
import CardMedia from '@material-ui/core/CardMedia';
import FacebookIcon from '@material-ui/icons/Facebook';
import LinkedInIcon from '@material-ui/icons/LinkedIn';
import InstagramIcon from '@material-ui/icons/Instagram';
import TwitterIcon from '@material-ui/icons/Twitter';
import { REQUIRED_ERROR } from 'utils/constants';
import { CustomerServices } from 'Services/CustomerServices';
import ABInBev from "../../Assets/brandLogos/ABInBev.png";
import American_Tower from "../../Assets/brandLogos/American_Tower_Corporation_Logo.png";
import BCX from "../../Assets/brandLogos/BCX.png";
import CSIR from "../../Assets/brandLogos/CSIR.jpeg";
import NaspersLabs from "../../Assets/brandLogos/NaspersLabs.png";
import RAIN from "../../Assets/brandLogos/RAIN.png";
import SARS from "../../Assets/brandLogos/SARS.png";
import SEACOM from "../../Assets/brandLogos/SEACOM.png";
import Sanlam from "../../Assets/brandLogos/Sanlam.png";
import Sasbo from "../../Assets/brandLogos/Sasbo.svg.png";
import 'Sys/config.js';
import { useAuthState } from 'Context';
import { Paths } from 'Routes/routePaths';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import moderEduBGImg from '../../Assets/images/homePage/bgImg1.png';
import ModernEdu from '../../Assets/images/homePage/ApplyOnline.png';
import accreditionLogo from '../../Assets/images/homePage/DEVIARE_Accreditations.png';
import { getDescription } from 'Pages/CourseListing';
import PopularCoursesCarousel from 'Components/PopularCoursesCarousel';
import exploreCrsImg from 'Assets/images/homePage/exploreBg1.png';
import Microsoft from 'Assets/brandLogos/microsoft.jpg';
import Sasol from 'Assets/brandLogos/sasol.jpg';
import Mimecast from 'Assets/brandLogos/mimecast.jpg';
import FoundationLogo from 'Assets/brandLogos/FoundationLogo.jpg';
import Inova from 'Assets/brandLogos/logo-white.png';
import PhoneInputField from 'Components/PhoneInputField';
import { CoursesServices } from 'Services/CoursesServices';
import { useKeycloak } from '@react-keycloak/web';
import { initOptions } from '../../config/keycloak'; // Add this import
import PricingPlans from './PricingPlans';

const customerService = new CustomerServices();
const coursesService = new CoursesServices();
const isDev = (process.env.REACT_APP_API_ENDPOINT !== "https://api-prod.deviare.africa/");
console.log("isDev ========== : ", isDev);

const useStyles = makeStyles((theme) => ({
    bannerWrap: {
        background: '#FAFCFF',
        display: "flex",
        minHeight: 478,
        width: "100%",
        marginBottom: 20
    },
    content1: {
        width: 'calc(100% - 260px)',
        padding: 0,
        maxWidth: '100%'
    },
    mobileContent: {
        padding: '10px 20px',
        maxWidth: 1040
    },
    content: {
        // width: 'calc(100% - 260px)',
        padding: 25,
        maxWidth: 1040
    },
    imgWrap: {
        width: "100%",
    },
    imgPart1: {
        position: "absolute",
        top: 70,
        right: 0
    },
    imgPart2: {
        position: "absolute",
        top: 235,
        right: 0
    },
    mainTitle: {
        fontWeight: 700,
        fontSize: 40,
        paddingTop: 85
    },
    courseWrap: {
        width: 250,
        height: 300,
        backgroundColor: '#1f1e1e',
        borderRadius: 5,
        position: 'relative',
        overflow: 'hidden',
        cursor: "pointer",
        display: 'flex',
        '&:hover .hoverColr': {
            display: 'block'
        },
        '&:hover img': {
            transform: 'scale(1.2)',
            top: -45
        },
        '&:hover .courseTitle': {
            top: 0,
            zIndex: 999
        },
        '&:hover .courseDesc': {
            visibility: 'visible',
            opacity: 1,
        },
    },
    courseImgWrap: {
        // margin: 'auto',
        width: '100%',
    },
    overlayImg: {
        position: "absolute",
        top: 155,
        right: 0,
        maxWidth: '100%',
        transition: 'all 0.3s',
        display: 'block',
        width: '100%',
        height: 'auto',
        transform: 'scale(1)',
    },
    courseImg: {
        maxWidth: '100%',
        transition: 'all 0.3s',
        display: 'block',
        width: '100%',
        height: '100%',
        transform: 'scale(1)'
    },
    learnMoreBtn: {
        position: 'absolute',
        bottom: 20,
        right: 20,
        borderRadius: 5
    },
    media: {
        height: 245,
    },
    secondaryTitle: {
        color: '#808080',
        fontWeight: 400,
        lineHeight: '20px'
    },
    contactBorder: {
        // borderBottom: '2px solid #d6d0d0',
        marginBottom: 34,
        width: '100%'
    },
    inputStyle: {
        minWidth: 120,
        borderColor: '#838383',
        borderRadius: '25px'
    },
    contactTitle: {
        fontSize: 20,
        paddingBottom: 10
    },
    socialIcons: {
        '& .MuiIconButton-root': {
            backgroundColor: '#3d7ddd',
            borderRadius: '50%',
            color: '#fff',
            marginRight: 10,
            '&:hover': {
                backgroundColor: '#1f4f99',
            }
        }
    },
    contactFormLabel: {
        lineHeight: '20px',
        fontSize: 14
    },
    newslatterButtons: {
        color: '#fff',
        '& .MuiIconButton-colorPrimary': {
            background: 'none',
            border: 'none',
            '& :hover': {
                color: '#3D7DDD',
            }
        }
    },
    moderEduWrap: {
        backgroundImage: `url(${moderEduBGImg})`,
        backgroundColor: '#0080CA',
        backgroundRepeat: 'round',
        display: "flex",
        minHeight: 280,
        width: "100%",
        position: 'relative',
        marginBottom: 20,
        '& .modernEduTitle': {
            color: '#fff',
            fontWeight: 700,
            fontSize: 40,
            lineHeight: '44px',
            paddingBottom: 10
        },
        '& .modernEduSubTitle': {
            color: '#fff',
            fontWeight: 'normal',
            fontSize: 15,
            lineHeight: '20px',
            paddingBottom: 10
        },
        '& img': {
            position: 'absolute',
            top: -50
        }
    },
    academyAccredWrapper: {
        backgroundColor: '#F9FAFB',
        marginTop: 20,
        marginBottom: 60,
        borderRadius: 24,
        minHeight: 305,
        padding: 25,
        maxWidth: 1040,
        position: 'relative',
        '& .accredSubTitle': {
            fontWeight: 'normal',
            fontSize: 15,
            lineHeight: '20px',
            paddingBottom: 10
        }
    },
    exploreCourseWrap: {
        backgroundImage: `url(${exploreCrsImg})`,
        backgroundRepeat: 'round',
        display: "flex",
        minHeight: 120,
        width: "100%",
        position: 'relative',
        marginBottom: 20,
        '& .modernEduTitle': {
            color: '#fff',
            fontWeight: 700,
            fontSize: 40,
            lineHeight: '44px',
            paddingBottom: 10,
            borderLeft: '4px solid #ec7a12',
            paddingLeft: 12
        },
        '& .modernEduSubTitle': {
            color: '#fff',
            fontWeight: 'normal',
            fontSize: 15,
            lineHeight: '20px',
            paddingBottom: 10,
        },
        '& .exploreLeftBorder': {
        }
    },
    phoneInputStyle: {
        marginBottom: 15,
        marginTop: 8,
        position: 'relative',
        '& .PhoneInputInput': {
            border: '1px solid #838383',
            borderRadius: '25px',
            padding: 16,
            paddingLeft: 48
        },
        '& .PhoneInputCountry': {
            position: 'absolute',
            left: 12,
            top: 18
        }
    },
}));

export const courseList = [
    {
        id: 'cs1',
        image: courseImg1,
        name: "Machine Learning with R",
        desc: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.'
    },
    {
        id: 'cs2',
        image: courseImg2,
        name: "Machine Learning with R",
        desc: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.'
    },
    {
        id: 'cs3',
        image: courseImg3,
        name: "Machine Learning with R",
        desc: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.'
    },
    {
        id: 'cs4',
        image: courseImg4,
        name: "Machine Learning with R",
        desc: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.'
    },
    {
        id: 'cs3',
        image: courseImg3,
        name: "Machine Learning with R",
        desc: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.'
    },
    {
        id: 'cs4',
        image: courseImg4,
        name: "Machine Learning with R",
        desc: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.'
    }
];

export const accreditedList = [
    {
        id: '115360',
        image: courseImg1,
        name: "Data Science & BI",
        desc: 'Apply the principles of creating a computer program using a procedural programming language in a GUI environment.'
    },
    {
        id: '115373',
        image: courseImg2,
        name: "Data Science with SAS",
        desc: 'Demonstrate an understanding of Computer Database Management Systems.'
    },
    {
        id: 'cs3',
        image: courseImg3,
        name: "Data Science with Python & Machine Learning",
        desc: 'Demonstrate an understanding of sort and search techniques used in computer programming.'
    },
    {
        id: '115367',
        image: courseImg4,
        name: "Tableau 10",
        desc: 'Demonstrate logical problem solving and error detection techniques.'
    },
    {
        id: '114049',
        image: courseImg1,
        name: "Haddop and Spark",
        desc: 'Demonstrate an understanding of Computer Database Management Systems.'
    },
    {
        id: '259277',
        image: courseImg2,
        name: "Capstone Project",
        desc: 'Perform requirements analysis.'
    }
];

export const testimonials = [
    {
        name: "Lorem ipsum",
        img: csImg,
        designation: "Project Manager, Deviare",
        desc: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."
    },
    {
        name: "Lorem ipsum 2",
        img: csImg,
        designation: "Project Manager, Deviare",
        desc: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."
    },
    {
        name: "Lorem ipsum 3",
        img: csImg,
        designation: "Project Manager, Deviare",
        desc: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."
    },
    {
        name: "Lorem ipsum 4",
        img: csImg,
        designation: "Project Manager, Deviare",
        desc: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."
    }
];

export const brandLogos = [
    ABInBev,
    American_Tower,
    BCX,
    CSIR,
    NaspersLabs,
    RAIN,
    SARS,
    SEACOM,
    Sanlam,
    Sasbo,
    Microsoft,
    Sasol,
    Mimecast,
    FoundationLogo,
    Inova
];

export const CourseItemPanel = ({ course }) => {
    const classes = useStyles();
    return (
        <Grid container spacing={4}>
            <Grid xs={5} item>
                <img style={{ width: '100%', height: '300px', borderRadius: 8 }} alt={course.name} src={course.image} />
            </Grid>
            <Grid xs={7} item style={{ position: 'relative' }}>
                <div style={{ paddingBottom: 20 }} >
                    {course.types.map(itm => (
                        <Chip
                            style={{ marginRight: 15, padding: "10px 20px" }}
                            size="small"
                            label={itm}
                            color="primary"
                        />
                    ))}
                </div>
                <Typography variant="subtitle2" className={classes.secondaryTitle}>{course.description}</Typography>
                <Button color="primary" variant="contained" className={classes.learnMoreBtn}>Learn More</Button>
            </Grid>
        </Grid>
    );
}

export const CoursesPanel = ({ courses }) => {
    const classes = useStyles();
    return (
        <Container style={{ backgroundColor: '#fff', borderRadius: 5 }} maxWidth="lg" className={classes.content}>
            <Grid container >
                <Grid item xs={12} style={{ marginBottom: 20 }}>
                    <Typography style={{ fontWeight: 700, fontSize: 32, paddingBottom: 20 }} variant="h4">Explore Our Courses</Typography>
                    <Typography variant="subtitle2" className={classes.secondaryTitle}>Our courses are perfect for you if you have a busy Ifestyle. You can lean at your own pace and follow our expert guidance. In half a year or less, you can gain practical skills hat will boost your career or business.</Typography>
                </Grid>
                <Grid item xs={12}>
                    <CustomTabs
                        tabs={courses.map((itm) => {
                            return {
                                label: itm.name,
                                children: <CourseItemPanel course={itm} type="course" />
                            };
                        })}
                    />
                </Grid>
            </Grid>
        </Container>
    )
}

export const ContactUsPanel = ({ formState, handleChange, handleSubmit, loading }) => {
    const classes = useStyles();
    const isMobile = IsMobileDevice('sm');

    return (
        <div id={'contactsec'}>
            <Container style={{ marginTop: (isMobile) ? 20 : 0, marginBottom: 30 }} className={(isMobile) ? classes.content : classes.mobileContent}>
                <Grid container spacing={6}>
                    {isMobile &&
                        <Grid item xs={5} style={{ marginTop: 10 }}>
                            <Card style={{ backgroundColor: '#f1f3f6', borderRadius: 12 }}>
                                <CardMedia
                                    className={classes.media}
                                    image={courseImg2}
                                    title="Contemplative Reptile"
                                />
                                <CardContent style={{ padding: 32 }}>
                                    <Grid container>
                                        <Grid xs={12} item>
                                            <Typography className={classes.contactTitle} variant="h4" color="primary">
                                                Contact Details
                                            </Typography>
                                            <Typography style={{ paddingBottom: 3 }} variant="subtitle2" className={classes.secondaryTitle}>
                                                <b>Email:</b> <a style={{ color: '#808080', textDecoration: 'none' }} href="mailto:<EMAIL>"><EMAIL></a>
                                            </Typography>
                                            <Typography style={{ paddingBottom: 3 }} variant="subtitle2" className={classes.secondaryTitle}>
                                                <b>Telephone:</b> <a style={{ color: '#808080', textDecoration: 'none' }} href='tel:+27105958522'>+27 10 595 8522</a>
                                            </Typography>
                                            <div className={classes.contactBorder}></div>
                                        </Grid>
                                    </Grid>
                                    <Grid container>
                                        <Grid xs={12} item>
                                            <Typography className={classes.contactTitle} variant="h4" color="primary">
                                                Our Location
                                            </Typography>
                                            <Typography variant="subtitle2" className={classes.secondaryTitle}>
                                                Block C, Cedar Tree Medical and Office Park, Fourways, Sandton, Gauteng, 2055
                                            </Typography>
                                            <div className={classes.contactBorder}></div>
                                        </Grid>
                                    </Grid>
                                    <Grid container>
                                        <Grid xs={12} item >
                                            <Typography className={classes.contactTitle} variant="h4" color="primary">
                                                Social Media
                                            </Typography>
                                            <div style={{ width: '100%' }} className={classes.socialIcons}>
                                                <IconButton aria-label="delete" onClick={() => window.open("https://twitter.com/DeviareSA")}>
                                                    <TwitterIcon />
                                                </IconButton>
                                                <IconButton aria-label="facebook" onClick={() => window.open("https://www.facebook.com/deviareafrica/")}>
                                                    <FacebookIcon />
                                                </IconButton>
                                                <IconButton aria-label="instagram" onClick={() => window.open("https://www.instagram.com/deviareafrica/")}>
                                                    <InstagramIcon />
                                                </IconButton>
                                                <IconButton aria-label="instagram" onClick={() => window.open("https://www.linkedin.com/company/deviare/")}>
                                                    <LinkedInIcon />
                                                </IconButton>
                                            </div>
                                        </Grid>
                                    </Grid>
                                </CardContent>
                            </Card>
                        </Grid>}
                    <Grid item xs={12} lg={7} sm={12}>
                        <Typography style={{ fontWeight: 700, fontSize: (isMobile) ? 36 : 20, paddingTop: 0 }} variant="h4">Get In Touch</Typography>
                        <Typography variant="subtitle2" className={classes.secondaryTitle}>
                            Complate the form to request a free call-back from one of our Enrolment Consultants.
                        </Typography>
                        <Grid container spacing={2} style={{ marginTop: 20 }}>
                            <Grid item xs={12} lg={6} sm={6}>
                                <Input
                                    disableUnderline
                                    type={'text'}
                                    className={classes.inputStyle}
                                    style={{ width: '100%' }}
                                    color={'secondary'}
                                    name="first_name"
                                    onChange={handleChange}
                                    value={formState?.first_name}
                                    placeholder='First Name *'
                                />
                            </Grid>
                            <Grid item xs={12} lg={6} sm={6}>
                                <Input
                                    disableUnderline
                                    type={'text'}
                                    className={classes.inputStyle}
                                    style={{ width: '100%' }}
                                    color={'secondary'}
                                    onChange={handleChange}
                                    name="last_name"
                                    value={formState?.last_name}
                                    placeholder='Last Name *'
                                />
                            </Grid>
                            <Grid item xs={12} lg={6} sm={6}>
                                <PhoneInputField
                                    handleChange={(newVal) => handleChange({ target: { value: newVal, name: 'mobile_no' } })}
                                    value={formState?.mobile_no}
                                    name="mobile_no"
                                    customClass={classes.phoneInputStyle}
                                />
                            </Grid>
                            <Grid item xs={12} lg={6} sm={6}>
                                <Input
                                    disableUnderline
                                    type={'text'}
                                    value={formState?.email}
                                    name="email"
                                    onChange={handleChange}
                                    className={classes.inputStyle}
                                    style={{ width: '100%', marginTop: 8 }}
                                    color={'secondary'}
                                    placeholder='Email *'
                                />
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid item xs={12} lg={6} sm={6}>
                                <OutlinedSelect
                                    val={formState?.work_status}
                                    name="work_status"
                                    styleOverrides={{ width: '100%' }}
                                    label={"Work Status *"}
                                    handleChange={handleChange}
                                    options={['Employed', 'Unemployed', 'Still at School']}
                                />
                            </Grid>
                            <Grid item xs={12} lg={6} sm={6}>
                                <OutlinedSelect
                                    val={formState?.study_type}
                                    name="study_type"
                                    styleOverrides={{ width: '100%' }}
                                    label={"What is your Motivation?"}
                                    handleChange={handleChange}
                                    options={['Career Change', 'Promotion', 'Personal Development', 'Skills Development']}
                                />
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <OutlinedSelect
                                    val={formState?.start_study_time}
                                    styleOverrides={{ width: '100%' }}
                                    label={"When do you want to start studying?"}
                                    handleChange={handleChange}
                                    name={"start_study_time"}
                                    options={['Immediately', '1 Month', '3 Months', '6 Months', '11 Months']}
                                />
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid item xs={6}>
                                <Typography style={{ marginTop: 10 }} variant="subtitle2" className={classes.contactFormLabel}>Send Newsletter/Articles: </Typography>
                            </Grid>
                            <Grid item xs={6}>
                                <RadioGroup value={formState.sendNewslatter} onChange={handleChange} className={classes.newslatterButtons} row aria-label="position" name="sendNewslatter">
                                    <FormControlLabel value="yes" control={<Radio color="primary" />} label="Yes" />
                                    <FormControlLabel value="no" control={<Radio color="primary" />} label="No" />
                                </RadioGroup>
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <Typography className={classes.contactFormLabel} variant="subtitle2">By clicking "Submit", you explicitly and actively consent to our processing of your personal information in accordance with our Privacy Policy.
                                    <br /> In order to continue please select the Accept checkbox below. </Typography>
                            </Grid>
                        </Grid>

                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            color="primary"
                                            checked={formState.acceptPolicy}
                                            onChange={handleChange}
                                            style={{ background: 'none' }}
                                            name="acceptPolicy"
                                        />
                                    }
                                    label={<a rel="noreferrer" href={Paths.PrivacyPolicy} style={{ textDecoration: 'none', fontWeight: 500 }} target='_blank' > Please accept our privacy policy *</a>}
                                />
                            </Grid>
                        </Grid>
                        <Grid container spacing={2} style={{ marginTop: 10 }}>
                            <Grid item xs={12}>
                                <Button disabled={loading} variant="contained" color="primary" style={{ borderRadius: 10, minWidth: 130, padding: '13px 5px' }} onClick={handleSubmit}>{loading ? <CircularProgress size={20} style={{ color: 'white' }} /> : 'Submit'}</Button>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </Container>
        </div>
    )
}

export const PopularCoursesPanel = ({ courses, loading }) => {
    const classes = useStyles();
    const history = useHistory();
    const isMobile = IsMobileDevice('sm');
    return (
        <Container style={{ marginTop: 20, borderRadius: 5 }} className={(isMobile) ? classes.content : classes.mobileContent}>
            <Grid container >
                <Grid item xs={12}>
                    <Typography style={{ fontWeight: 700, fontSize: (isMobile) ? 32 : 20 }} variant="h4">Popular Courses</Typography>
                    <Typography variant="subtitle2" className='accredSubTitle' >Find The Right Course For You</Typography>
                </Grid>
            </Grid>
            <Grid container >
                <Grid item xs={12}>
                    {(loading) ? <div style={{ width: '100%', margin: '20px', minHeight: 180 }}><CircularProgress /></div> :
                        <PopularCoursesCarousel handleClickItem={(course) => history.push(`/course/${course?.id}`)} list={courses} />}
                </Grid>
            </Grid>
        </Container>
    )
}

export const AcademyAccreditationPanel = () => {
    const classes = useStyles();
    const history = useHistory();
    const isMobile = IsMobileDevice('sm');

    return (
        <Container className={classes.academyAccredWrapper} style={(!isMobile) ? { padding: '0px 20px' } : null}>
            <Grid container >
                <Grid item xs={12} lg={5} sm={12}>
                    <img style={{ width: '350px' }} src={accreditionLogo} alt="accreditionLogo" />
                </Grid>
                <Grid item xs={12} lg={7} sm={12} style={{ margin: 'auto 0px' }}>
                    <Typography style={{ fontWeight: 700, fontSize: (isMobile) ? 32 : 20 }} variant="h4">Elevate Your Learning with Accredited Excellence</Typography>
                    <Typography variant="subtitle2" className='accredSubTitle' >Description: Deviare has received accreditation from three leading organizations in the field of IT education: MICT SETA, IITPSA, and UNISA. This recognition ensures that our courses are of the highest quality and meet the industry's highest standards.</Typography>
                    <Grid container style={{ marginTop: 22 }}>
                        <Grid item xs={12}>
                            <Button
                                variant="outlined"
                                aria-haspopup="true"
                                onClick={() => history.push(Paths.Accreditations)}
                                color="primary"
                                style={{ padding: '14px 45px', borderRadius: 5 }}>
                                Learn more
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>
        </Container>
    )
}

export const ModernEducationPanel = () => {

    const classes = useStyles();
    const history = useHistory();
    const isMobile = IsMobileDevice('sm');

    return (
        <div className={classes.moderEduWrap}>
            <Container className={(isMobile) ? classes.content : classes.mobileContent}>
                <Grid container spacing={2}>
                    <Grid style={(!isMobile) ? { paddingTop: 24, paddingBottom: 24 } : { margin: 'auto 0' }} item xs={12} sm={isMobile ? 6 : 12} lg={7}>
                        <Typography style={(!isMobile) ? { fontSize: 32 } : null} className={'modernEduTitle'} variant="h4"><span style={{ color: '#EC7A12' }}>Unlock </span>Your Career Potential with Personalized Learning Paths</Typography>
                        <Typography className='modernEduSubTitle'>Description: Deviare offers customized learning paths to help you develop the skills you need to succeed in your career. Our paths are flexible, personalized, and supported by our team of experts.</Typography>
                        <Grid container style={{ marginTop: 22 }}>
                            <Grid item xs={12}>
                                <Button
                                    variant="outlined"
                                    aria-haspopup="true"
                                    onClick={() => history.push('/course-listing')}
                                    style={{ background: '#F2F2F2', color: '#0080CA', padding: '14px 45px', borderRadius: 5 }}>
                                    Apply Online
                                </Button>
                            </Grid>
                        </Grid>
                    </Grid>
                    {isMobile &&
                        <Grid item xs={12} sm={12} lg={5}>
                            <img src={ModernEdu} style={{ width: 500 }} alt="Modern Education" />
                        </Grid>}
                </Grid>
            </Container>
        </div>
    )
}

export const ExploreOurCourses = () => {
    const classes = useStyles();
    const isMobile = IsMobileDevice('sm');

    return (
        <div className={classes.exploreCourseWrap}>
            <Container className={isMobile ? classes.content : classes.mobileContent}>
                <Grid container spacing={2}>
                    <Grid item style={(!isMobile) ? { paddingTop: 24 } : { margin: 'auto 0' }} xs={12} sm={isMobile ? 6 : 12} lg={4}>
                        <Typography className={'modernEduTitle'} style={(!isMobile) ? { fontSize: 32 } : null} variant="h4">Be Your Own Boss <span style={{ color: '#EC7A12' }}> Potential</span></Typography>
                    </Grid>
                    {/* <Grid item xs={12} lg={7} style={(!isMobile) ? { paddingBottom: 24 } : { margin: 'auto 0' }}>
                        <Typography className='modernEduSubTitle'>Deviare has received accreditation from three leading organizations in the field of IT education: MICT SETA, IITPSA, and UNISA. This recognition ensures that our courses are of the highest quality and meet the industry's highest standards.</Typography>
                    </Grid> */}
                </Grid>
            </Container>
        </div>
    )
}

export const accreditationCourses = {
    mict: [
        {
            name: "Data Science with R",
            isForBuy: true,
            uuid: 'a9512940-5866-461b-bdcf-e551870645bb',
            items: [{
                courseId: "115360",
                unitStandards: "Demonstrate fourth generation language computer programming skills",
                nQFLevel: "Level 5",
                credits: "7"
            },
            {
                courseId: "115387",
                unitStandards: "Apply the principles of creating a computer program using a procedural programming language in a GUI environment",
                nQFLevel: "Level 6",
                credits: "14"
            }]
        },
        {
            name: "Data Science with SAS",
            isForBuy: false,
            uuid: '22771085-8c9e-4ad1-86ce-3e8448ab5eea',
            items: [{
                courseId: "114049",
                unitStandards: "Demonstrate an understanding of Computer Database Management Systems",
                nQFLevel: "Level 5",
                credits: "7"
            },
            {
                courseId: "115373",
                unitStandards: "Demonstrate an understanding of sort and search techniques used in computer programming",
                nQFLevel: "Level 5",
                credits: "7"
            },
            {
                courseId: "115373",
                unitStandards: "Demonstrate an understanding of sort and search techniques used in computer programming",
                nQFLevel: "Level 5",
                credits: "7"
            }
            ]
        },
        {
            name: "Data Science with Python & Machine Learning",
            isForBuy: true,
            uuid: '453c5e6b-36b2-413b-8505-881d11e9631b',
            items: [{
                courseId: "115381",
                unitStandards: "Apply the principles of creating a computer program using an OOP language in a GUI environment",
                nQFLevel: "Level 6",
                credits: "12"
            },
            {
                courseId: "115382",
                unitStandards: "Apply the principles of creating computer programs containing advanced algorithms using a procedural programming language",
                nQFLevel: "Level 6",
                credits: "12"
            },
            {
                courseId: "115378",
                unitStandards: "Demonstrate an understanding of advanced object-oriented programming",
                nQFLevel: "Level 6",
                credits: "14"
            }]
        },
        {
            name: "Tableau 10",
            isForBuy: true,
            uuid: 'da298826-80b2-459d-b741-1faac13995f5',
            items: [{
                courseId: "115367",
                unitStandards: "Demonstrate logical problem solving and error detection techniques",
                nQFLevel: "Level 5",
                credits: "8"
            }]
        },
        {
            name: "Haddop and Spark",
            isForBuy: true,
            uuid: '6cec4803-c005-466c-8cd5-b3b006fc8989',
            items: [{
                courseId: "114049",
                unitStandards: "Demonstrate an understanding of Computer Database Management Systems",
                nQFLevel: "Level 5",
                credits: "7"
            }]
        },
        {
            name: "Capstone Project",
            isForBuy: false,
            items: [{
                courseId: "259277",
                unitStandards: "Perform requirements analysis",
                nQFLevel: "Level 6",
                credits: "25"
            }]
        }
    ],
    iitpsa: [
        {
            name: "Data Science & BI",
            items: [{
                courseId: "DEV001",
                unitStandards: "Data Science",
                nQFLevel: "Level 1",
                credits: "1"
            }]
        },
        {
            name: "AI & Machine Learning",
            items: [{
                courseId: "DEV002",
                unitStandards: "AI Engineer",
                nQFLevel: "Level 1",
                credits: "1"
            }]
        },
        {
            name: "Digital Marketing",
            items: [{
                courseId: "DEV003",
                unitStandards: "Digital Marketing Associate",
                nQFLevel: "Level 1",
                credits: "1"
            }]
        },
        {
            name: "DevOps",
            items: [{
                courseId: "DEV004",
                unitStandards: "DevOps Engineer",
                nQFLevel: "Level 1",
                credits: "1"
            }]
        },
        {
            name: "Project Management",
            items: [{
                courseId: "DEV005",
                unitStandards: "Digital Project Manager",
                nQFLevel: "Level 1",
                credits: "1"
            },
            {
                courseId: "DEV006",
                unitStandards: "Business Analyst",
                nQFLevel: "Level 1",
                credits: "1"
            }]
        },
        {
            name: "Software Development",
            items: [{
                courseId: "DEV007",
                unitStandards: "Full Stack Java Developer",
                nQFLevel: "",
                credits: "1"
            }]
        }
    ],
    unisa: [
        {
            name: "Azure Pricing and Support",
            items: [{
                courseId: "CCAPS03",
                unitStandards: "Data Science",
                nQFLevel: "Level 4",
                credits: "1"
            }]
        },
        {
            name: "Cloud concept and core Azure services",
            items: [{
                courseId: "CCCASO1",
                unitStandards: "AI Engineer",
                nQFLevel: "",
                credits: "1"
            }]
        },
        {
            name: "Security Privacy Compliance and Trust",
            items: [{
                courseId: "CSPCT02",
                unitStandards: "Digital Marketing Associate",
                nQFLevel: "",
                credits: "1"
            }]
        }
    ]
};

export const ChoosePlan = () => {
    const classes = useStyles();
    const isMobile = IsMobileDevice('sm');

    return (
        <div className={classes.exploreCourseWrap}>
            <Container className={isMobile ? classes.content : classes.mobileContent}>
                <Grid container spacing={2}>
                    <Grid item style={(!isMobile) ? { paddingTop: 24 } : { margin: 'auto 0' }} xs={12} sm={isMobile ? 6 : 12} lg={4}>
                        <Typography className={'modernEduTitle'} style={(!isMobile) ? { fontSize: 32 } : null} variant="h4">Choose your learning plan <span style={{ color: '#EC7A12' }}> Potential</span></Typography>
                    </Grid>
                    {/* <Grid item xs={12} lg={7} style={(!isMobile) ? { paddingBottom: 24 } : { margin: 'auto 0' }}>
                        <Typography className='modernEduSubTitle'>Deviare has received accreditation from three leading organizations in the field of IT education: MICT SETA, IITPSA, and UNISA. This recognition ensures that our courses are of the highest quality and meet the industry's highest standards.</Typography>
                    </Grid> */}
                </Grid>
            </Container>
        </div>
    )
}
export const PlatformFeaturesPanel = () => {
  const features = [
    {
      title: 'Certifications',
      desc: 'Lorem ipsum dolor sit amet consectetur. Rhoncus placerat elementum hendrerit donec eu. Amet eu molestie et viverra sed maecenas fringilla.',
      button: 'Choose Your Path',
      onClick: () => {},
    },
    {
      title: 'Course Offerings',
      desc: 'Lorem ipsum dolor sit amet consectetur. Rhoncus placerat elementum hendrerit donec eu. Amet eu molestie et viverra sed maecenas fringilla.',
      button: 'Explore Courses',
      onClick: () => {},
    },
    {
      title: 'Career Assessment',
      desc: 'Lorem ipsum dolor sit amet consectetur. Rhoncus placerat elementum hendrerit donec eu. Amet eu molestie et viverra sed maecenas fringilla.',
      button: 'Take The Assessment',
      onClick: () => {},
    },
    {
      title: 'Hands on Labs',
      desc: 'Lorem ipsum dolor sit amet consectetur. Rhoncus placerat elementum hendrerit donec eu. Amet eu molestie et viverra sed maecenas fringilla.',
      button: 'Explore Certifications',
      onClick: () => {},
    },
  ];

  return (
    <div style={{ margin: '64px 0' }}>
      <Container maxWidth="lg">
        <Grid container spacing={4} alignItems="center">
          <Grid item xs={12} md={5}>
            <Typography style={{ color: '#888', fontSize: 20, fontWeight: 400, lineHeight: 1.2, maxWidth: 480 }}>
              Deviare offers online courses designed by subject matter experts to equip you with the practical, industry-relevant digital skills that employers demand in today's tech-driven world. Our platform is dedicated to empowering self-motivated individuals who are ready to invest in their own professional development and advance their careers through continuous professional development.
            </Typography>
          </Grid>
          <Grid item xs={12} md={7}>
            <Grid container spacing={4}>
              {features.map((f, i) => (
                <Grid item xs={12} sm={6} key={f.title}>
                  <div style={{
                    background: '#fff',
                    borderRadius: 20,
                    boxShadow: '0 6px 18px 0 rgba(60,60,100,0.12)',
                    padding: '28px 28px 24px 28px',
                    minHeight: 220,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                  }}>
                    <Typography style={{ fontWeight: 700, fontSize: 26, marginBottom: 12 }}>
                      {f.title}
                    </Typography>
                    <Typography style={{ color: '#444', fontSize: 16, marginBottom: 24, minHeight: 60 }}>
                      {f.desc}
                    </Typography>
                    <Button
                      variant="contained"
                      fullWidth
                      style={{
                        background: '#4a7fcf',
                        color: '#fff',
                        borderRadius: 12,
                        fontWeight: 700,
                        fontSize: 18,
                        padding: '10px 0',
                        textTransform: 'none',
                        boxShadow: 'none',
                      }}
                      onClick={f.onClick}
                    >
                      {f.button}
                    </Button>
                  </div>
                </Grid>
              ))}
            </Grid>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
};
export const initContactFields = { first_name: '', last_name: '', mobile_no: '', work_status: '', study_type: '', start_study_time: '', acceptPolicy: false, email: '', sendNewslatter: '' };

const MainPage = ({ isHomePage }) => {
    const { keycloak, initialized } = useKeycloak();
    const history = useHistory();
    const userDetails = useAuthState();

    const handleLogin = async () => {
        try {
            if (!initialized) {
                await keycloak.init(initOptions);
            }

            await keycloak.login({
                redirectUri: `${window.location.origin}/home`,
                prompt: 'login',
                scope: 'openid'
            });
        } catch (error) {
            console.error('Login error:', error);
            // Fallback login attempt without iframe checking
            keycloak.login({
                redirectUri: `${window.location.origin}/home`,
                prompt: 'login',
                scope: 'openid',
                checkLoginIframe: false
            });
        }
    };

    // Handle authentication state changes
    React.useEffect(() => {
        if (initialized && keycloak.authenticated) {
            // Remove hash from URL
            if (window.location.hash) {
                history.replace('/home');
            }
            history.push(Paths.HomePage);
        }
    }, [initialized, keycloak.authenticated, history]);

    // Redirect if already authenticated
    React.useEffect(() => {
        if (initialized && keycloak.authenticated) {
            history.push(Paths.HomePage);
        }
    }, [initialized, keycloak.authenticated, history]);

    React.useEffect(() => {
        if (!isHomePage) {
            setTimeout(() => {
                const href = window.location.href.substring(
                    window.location.href.lastIndexOf('#') + 1
                );
                const element = document.getElementById(href);
                if (element) {
                    element.scrollIntoView({ behavior: "smooth" });
                }
            }, 500);
        }
        // eslint-disable-next-line
    }, [location]);

    React.useEffect(() => {
        // if (!isHomePage && Boolean(userDetails.token)) {
        //     history.push(Paths.HomePage);
        // }
        getCourseServiceList();
        // eslint-disable-next-line
    }, [userDetails]);

    const classes = useStyles();
    const [openModalType, setOpenModalType] = React.useState('');
    const [contactFormState, setContactFormState] = React.useState({ ...initContactFields });
    const [contactFormLoading, setContactFormLoading] = React.useState(false);
    const isMobile = IsMobileDevice('sm');
    const [popularCourseLoading, setPopularCourseLoading] = React.useState(false);
    const [popularCourses, setPopularCourses] = React.useState([]);

    const handleContactSubmit = async () => {
        if (!contactFormState.email || !contactFormState.first_name || !contactFormState.last_name || !contactFormState.mobile_no || !contactFormState.work_status || !contactFormState.study_type || !contactFormState.start_study_time) {
            notify('error', REQUIRED_ERROR);
            return null;
        }
        if (!contactFormState.acceptPolicy) {
            notify('error', 'Please Accept Terms And Condition');
            return null;
        }
        setContactFormLoading(true);
        const postData = { ...contactFormState };
        delete postData.acceptPolicy;
        postData.newsletter = (contactFormState?.sendNewsLatter === 'yes') ? true : false;
        const result = await customerService.sendContactMail(postData);
        if (result) {
            setContactFormState({ ...initContactFields });
            notify('success', 'Your Request is submitted successfully!');
        }
        setContactFormLoading(false);
    }

    const getCourseServiceList = async () => {
        setPopularCourseLoading(true);
        const csList = await coursesService.getEcommerceCourses(`popular=true`);
        if (csList && csList.status) {
            const courses = [];
            csList?.data?.map((cat) => {
                return cat?.courses?.map(((itm) => {
                    let description = itm.course_overview || getDescription(itm.description);
                    let sortDescription = (description.length > 80) ? `${description.slice(0, 80)}...` : description;
                    courses.push({
                        id: itm.uuid,
                        image: getCourseImage({ ...itm }),
                        name: (itm?.name && itm?.name.length > 50) ? `${itm?.name.slice(0, 50)}...` : itm?.name,
                        desc: sortDescription,
                        fullDesc: description
                    });
                    return itm;
                }));
            });
            setPopularCourses(courses);
        }
        setPopularCourseLoading(false);
    };

    return (
        <Layout
            openModalAction={openModalType}
            handleEmptyModalAction={() => setOpenModalType('')}
            onLogin={handleLogin}
        >
            <main style={{ backgroundColor: '#fff' }}>
                <div className={classes.bannerWrap}>
                    <Container className={isMobile ? classes.content : classes.mobileContent}>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={isMobile ? 6 : 12} lg={6}>
                                <Typography style={(!isMobile) ? { padding: '20px 0px', fontSize: 22 } : null} className={classes.mainTitle} variant="h4"><span style={{ color: '#0080CA' }}>Thrive </span>in the Digital Age with our Tailored Learning Experience!</Typography>
                                <Typography variant="subtitle1" className={classes.secondaryTitle}>
                                    Our innovative blended learning approach combines self-paced modules with virtual live classes, allowing you to learn at your own pace while receiving expert guidance. Whether you're looking to start, switch, or advance your career, our practical courses will equip you with the digital skills needed to future-proof your success.
                                </Typography>
                                <Grid container style={{ marginTop: 32 }}>
                                    <Grid item xs={12} lg={4} sm={12}>
                                        <Button
                                            variant="outlined"
                                            color="primary"
                                            aria-haspopup="true"
                                            onClick={() => history.push('/course-listing')}
                                            style={{ padding: '14px 25px', borderRadius: 5 }}>
                                            Explore More
                                        </Button>
                                    </Grid>
                                    {/* <Grid item xs={1}>
                                        <PlayCircleFilledWhiteIcon style={{cursor: 'pointer', fontSize: 48, color: '#F2994A'}}/>
                                    </Grid> */}
                                </Grid>
                            </Grid>
                            <Grid item xs={12} sm={isMobile ? 6 : 12} lg={6}>
                                <div className={classes.imgWrap}>
                                    <img src={sliderImg} alt="Deviare logo" style={{ width: '100%', marginTop: 10, borderRadius: 5 }} />
                                </div>
                            </Grid>
                        </Grid>
                    </Container>
                </div>
                <ExploreOurCourses />
                <PopularCoursesPanel loading={popularCourseLoading} courses={popularCourses} />
                {/* <AcademyAccreditationPanel /> */}
                <ChoosePlan/>
                <PricingPlans/>
                <PlatformFeaturesPanel />
                {/* <ModernEducationPanel /> */}
                <ContactUsPanel
                    formState={contactFormState}
                    handleChange={(evt) => {
                        let { name, value } = evt.target;
                        if (name === 'acceptPolicy') {
                            value = evt.target.checked;
                        }
                        setContactFormState({ ...contactFormState, [name]: value });
                    }}
                    loading={contactFormLoading}
                    handleSubmit={handleContactSubmit}
                />
                <Container className={(isMobile) ? classes.content : classes.mobileContent}>
                    <Grid container >
                        <Typography style={{ fontWeight: 700, fontSize: (!isMobile) ? 20 : 32, marginBottom: 30 }} variant="h4">Our learning professionals work for these top brands</Typography>
                    </Grid>
                    <Grid container>
                        <Carousel type="brandLogo" list={[...brandLogos]} />
                    </Grid>
                </Container>
            </main>
        </Layout>
    );
}

export default MainPage;