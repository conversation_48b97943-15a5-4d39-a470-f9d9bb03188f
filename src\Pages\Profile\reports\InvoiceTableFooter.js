import React from 'react';
import {Text, View, StyleSheet } from '@react-pdf/renderer';

const styles = StyleSheet.create({
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        height: 24,
        fontSize: 12,
        marginTop: 10,
        fontStyle: 'bold',
    },
    rowTotal: {
        flexDirection: 'row',
        borderBottomColor: '#9696A4',
        borderBottomWidth: 1,
        borderStyle: 'dashed',
        alignItems: 'center',
        height: 24,
        fontSize: 12,
        paddingBottom: 5,
        fontStyle: 'bold',
    },
    
    description: {
        width: '80%',
        textAlign: 'right',
        paddingRight: 8,
    },
    total: {
        width: '20%',
        textAlign: 'right',
        paddingRight: 8,
    },
    totalBal: {
        width: '20%',
        textAlign: 'right',
        paddingRight: 8,
        fontSize: '15px',
        fontWeight: 800
    },
    taxDescription: {
        width: '100%',
        textAlign: 'right'
    },

  });


const InvoiceTableFooter = ({ total }) => {
    return(
        <React.Fragment>
            <View style={styles.rowTotal}>
                <Text style={styles.description}>TOTAL : </Text>
                <Text style={styles.total}>{ total }</Text>
            </View>
            <View style={styles.row}>
                <Text style={styles.description}>TOTAL PAID : </Text>
                <Text style={styles.totalBal}>ZAR { total }</Text>
            </View>
            <View style={styles.row}>
                <Text style={styles.taxDescription}>* The above amount is inclusive of all applicable Taxes.</Text>
            </View>
        </React.Fragment>
    )
};
  
export default InvoiceTableFooter