/* eslint-disable */
import * as React from 'react';
import Layout from "Components/layouts";
import aiImg from 'Assets/images/ai.png';
import { CircularProgress, Container, Grid, LinearProgress } from '@material-ui/core';
import { makeStyles, withStyles } from '@material-ui/core/styles';
import { SkillsAndTransformation } from 'Services/SkillsAndTransformation';
import { CoursesServices } from 'Services/CoursesServices';
import { handleSSOAuth } from 'Services/AuthService';
import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';
import Typography from '@material-ui/core/Typography';
import { Button } from '@material-ui/core';
import { useLocation, useHistory } from "react-router-dom";
import { Paths } from 'Routes/routePaths';

const skillsAndTransformation = new SkillsAndTransformation();
const courseServices = new CoursesServices();

export const calculatePercentage = (val, total) => {
    let percentage = parseFloat((val / total)) * 100;
    return percentage ? Math.floor(percentage) : 0;
}

export const BorderLinearProgress = withStyles((theme) => ({
    root: {
        height: 10,
        borderRadius: 5
    },
    colorPrimary: {
        backgroundColor:
            theme.palette.grey[theme.palette.type === "light" ? 200 : 700]
    },
    bar: {
        borderRadius: 5,
        backgroundColor: "#EC7A12"
    }
}))(LinearProgress);

export const BorderLinearProgressComplete = withStyles((theme) => ({
    root: {
        height: 10,
        borderRadius: 5
    },
    colorPrimary: {
        backgroundColor:
            theme.palette.grey[theme.palette.type === "light" ? 200 : 700]
    },
    bar: {
        borderRadius: 5,
        backgroundColor: "#06AD17"
    }
}))(LinearProgress);

const useStyles = makeStyles((theme) => ({
    root: {
        display: 'flex',
        background: '#FFFFFF',
        boxShadow: "0px 4px 30px rgba(12, 41, 84, 0.1)",
        borderRadius: 5,
        padding: 10,
        paddingTop: 18,
        width: '100%',
        marginTop: 20
    },
    contentWrap: {
        width: 'calc(100% - 260px)',
        minWidth: '95%'
    },
    content: {
        flex: '1 0 auto',
        maxWidth: '80%',
    },
    cover: {
        width: '100%',
        height: 175,
        borderRadius: 5,
        // paddingTop: 17,
        // marginTop: 25,
        // marginLeft: 20,
        // maxWidth: '20%'
    },
    cardTitle: {
        color: "#808080"
    },
    cardDesc: {
        color: "#808080",
    },
    browseAllBtn: {
        borderRadius: 5,
        cursor: 'pointer',
        padding: '15px 20px',
        marginTop: 10
    },
}));

const RegisteredCourses = () => {

    const classes = useStyles();
    const [registerCourseList, setRegisterCourseList] = React.useState([]);
    const [loader, setLoader] = React.useState(true);
    const location = useLocation();
    const history = useHistory();
    const queryParams = new URLSearchParams(location.search);
    const type = queryParams.get("type") || null;

    React.useEffect(() => {
        setRegisterCourseList([]);
        setLoader(true);
        getAllRegisterCourse();
        // eslint-disable-next-line
    }, [location]);

    const getAllRegisterCourse = async () => {
        const courseList = await skillsAndTransformation.getRegisterCourse();
        setLoader(false);
        if (courseList.status) {
            const courseArr = [];
            // courseList.data.forEach((cat) => {
            //     if (cat.category !== 'Work readiness') {
            //         cat.data.forEach((course) => {
            //             let obj = {
            //                 title: course.category,
            //                 subTitle: course.course_name,
            //                 desc: course.description,
            //                 image: aiImg,
            //                 total: 100,
            //                 value: course.course_completion,
            //                 width: 12,
            //                 page: 'registeredCourse',
            //                 progress: true,
            //                 provider: course.provider,
            //                 ...course
            //             }

            //             if (type === "inprogress" && parseFloat(course.course_completion) < 100 && parseFloat(course.course_completion) > 0) {
            //                 courseArr.push(obj);
            //             } else if (type === "completed" && parseFloat(course.course_completion) >= 100) {
            //                 courseArr.push(obj);
            //             } else if (type === null) {
            //                 courseArr.push(obj);
            //             }
            //         });
            //     }
            // });
            courseList.data?.courses.forEach((cat) => {
                if (cat.category !== 'Work readiness') {
                    cat.data.forEach((item) => {
                        let { course } = item;
                        let obj = {
                            isLab: false,
                            title: course.category,
                            subTitle: course.course_name,
                            desc: course.description,
                            image: aiImg,
                            total: 100,
                            value: course.course_completion,
                            width: 12,
                            page: 'registeredCourse',
                            progress: true,
                            provider: course.provider,
                            ...course
                        };
                        if (type === "inprogress" && parseFloat(course.course_completion) < 100 && parseFloat(course.course_completion) > 0) {
                            courseArr.push(obj);
                        } else if (type === "completed" && parseFloat(course.course_completion) >= 100) {
                            courseArr.push(obj);
                        } else if (type === null) {
                            courseArr.push(obj);
                        }
                    });
                }
            });
            setRegisterCourseList(courseArr);
        }
    }

    const handleLinkURL = (itm) => {
        const { link, _, course_id_talent_lms, course_id } = itm;
        if (course_id_talent_lms) {
            try {
                courseServices.getSessionLinkToCourse(course_id)
                    .then(({ goto_url }) => {
                        window.open(goto_url);
                    })
                    .catch(error => {
                        console.error(error);
                    });
            } catch (error) {
                // Send push notification
                console.error(error);
            }
        } else {
            handleSSOAuth(link, 'simplilearn');
        }
    };

    return (
        <Layout sidebarView={false}>
            <main style={{ background: "#fff" }}>
                <Container className={classes.contentWrap} >
                    {loader && <div style={{ marginTop: 30, textAlign: "center", width: "100%" }}>
                        <CircularProgress />
                    </div>}
                    <Grid container spacing={2} style={{ marginBottom: 36 }}>
                        {registerCourseList.length ? registerCourseList.map((item) => {
                            return (<Grid item xs={12}>
                                <Card key={item.title} className={classes.root}>
                                    <CardContent className={classes.content} style={{ maxWidth: '100%' }}>
                                        <Grid container spacing={2}>
                                            <Grid item xs={12} sm={12} md={2} lg={2}>
                                                <img onClick={() => history.push(`/course/${item.course_id}`)} style={{ cursor: 'pointer' }} className={classes.cover} alt={item.title} src={item.image} />
                                            </Grid>
                                            <Grid item xs={12} sm={12} md={7} lg={7} style={{ marginLeft: 10 }}>
                                                <Typography className={classes.cardTitle}>
                                                    {item.title}
                                                </Typography>
                                                <Typography onClick={() => history.push(`/course/${item.course_id}`)} style={{ cursor: 'pointer' }} variant="h1" color="primary">
                                                    {item.subTitle}
                                                </Typography>
                                                <Typography className={classes.cardDesc} title={item.desc}>
                                                    {item.desc}
                                                </Typography>
                                                <Typography variant="h1" color="primary">
                                                    {item.totalCount}
                                                </Typography>

                                                {item.progress ? calculatePercentage(item.value, item.total) !== 100 ? <BorderLinearProgress variant="determinate" value={calculatePercentage(item.value, item.total)} /> :
                                                    <BorderLinearProgressComplete variant="determinate" value={calculatePercentage(item.value, item.total)} /> : null}

                                                <Typography style={{ fontSize: 13, color: "#808080", marginTop: 6 }} color="primary">
                                                    {calculatePercentage(item.value, item.total) + "% " + "completed"}
                                                </Typography>
                                            </Grid>
                                            <Grid item xs={12} sm={12} md={2} lg={2} style={{ margin: 'auto' }}>
                                                <Button
                                                    variant="outlined"
                                                    color="primary"
                                                    onClick={() => handleLinkURL(item)}
                                                    style={{ width: '100%', padding: '15px 45px', borderRadius: 10 }}>
                                                    {parseFloat(item.value) > 0 ? (
                                                        "Continue learning"
                                                    ) : (
                                                        "LAUNCH"
                                                    )}
                                                </Button>
                                            </Grid>
                                        </Grid>
                                    </CardContent>
                                </Card>
                            </Grid>)
                        }) : (!loader) ? <Grid item xs={12} style={{ textAlign: "center" }}>
                            <Typography style={{ marginTop: 40 }} variant='h4' >You don't have any registered courses</Typography>
                            <Button
                                variant="outlined"
                                color="primary"
                                className={classes.browseAllBtn}
                                onClick={() => history.push(Paths.CourseListing)}
                            >
                                Explore Now
                            </Button>
                        </Grid> : null}
                    </Grid>
                </Container>
            </main>
        </Layout>
    );
}

export default RegisteredCourses;