import React, { useEffect, useRef, useState } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import MenuItem from '@material-ui/core/MenuItem';
import {
    AppBar,
    Toolbar,
    IconButton,
    Button,
    Avatar,
    Typography,
    Grid,
    CircularProgress
} from '@material-ui/core';
import { NavLink } from 'react-router-dom';
import logoImg from '../../../Assets/images/logo.jpg';
import mobileLogo from '../../../Assets/images/deviare-minimum-logo.png';
import { logout, useAuthDispatch, useAuthState } from '../../../Context';
// import NotificationsIcon from '@material-ui/icons/Notifications';
// import Badge from '@material-ui/core/Badge';
import userImg from '../../../Assets/images/loading-waiting.gif';
import defaultImage from '../../../Assets/images/customer-default-logo.png';
import ClickAwayListener from '@material-ui/core/ClickAwayListener';
import Grow from '@material-ui/core/Grow';
import Paper from '@material-ui/core/Paper';
import Popper from '@material-ui/core/Popper';
import MenuList from '@material-ui/core/MenuList';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import ExpandLessIcon from '@material-ui/icons/ExpandLess';
import { Paths } from '../../../Routes/routePaths'
import EditIcon from '@material-ui/icons/Edit';
import { UserServices } from '../../../Services/UserServices';
import { useHistory, useLocation } from 'react-router';
import variables from '../../../Sys/variable.scss';
import { CoursesServices } from 'Services/CoursesServices';

import { IsMobileDevice, getEncodeURL } from 'utils';
import CategoryDrawer from 'Components/CategoryDrawer';
import DehazeIcon from '@material-ui/icons/Dehaze';
import MenuBookIcon from '@material-ui/icons/MenuBook';
import expDevLogo from 'Assets/images/homePage/expDeviareLogo.png';
import { SkillsAndTransformation } from 'Services/SkillsAndTransformation';
import { useKeycloak } from '@react-keycloak/web';

const skillsAndTransformation = new SkillsAndTransformation();
const userServices = new UserServices();
const coursesService = new CoursesServices();

const useStyles = makeStyles((theme) => ({
    root: {
        flexGrow: 1,
    },
    headerBar: {
        background: '#FAFCFF',
        height: 105,
        boxShadow: "none"
    },
    menuButton: {
        marginRight: theme.spacing(2),
    },
    title: {
        flexGrow: 1,
    },
    searchBar: {
        flexGrow: 1,
        marginLeft: 35,
        display: 'inline-flex',
        alignItems: 'center',
        gap: 10,
        marginTop: 20
    },
    iconBtn: {
        borderRadius: "50%",
        marginLeft: 10
    },
    profileImg: {
        width: 80,
        height: 80,
        margin: "0 auto",
        overflow: "hidden",
        border: "1px solid",
        borderRadius: "50%",
        backgroundColor: "#262626"
    },
    editBack: {
        position: "absolute",
        width: 75,
        height: 26,
        bottom: 0,
        textAlign: "center",
        paddingTop: 6,
        color: "#fff",
        background: "rgba(255, 255, 255, 0.6)"
    },
    rightMenuItem: {
        width: '100%',
        position: 'relative',
        textAlign: 'right',
        margin: 'auto',
        '&:hover': {
            color: '#3C7DDD'
        }
    },
    mainMenuItem: {
        width: '50%',
        position: 'relative',
        marginLeft: '35px',
        marginTop: 20,
        '&:hover': {
            color: '#3C7DDD'
        }
    },
    menuContainer: {
        display: 'flex',
        gridTemplateColumns: 'auto auto auto auto',
        backgroundColor: 'white',
        height: '600px',
        width: "100%",
        marginTop: 25,
        padding: 35,
        '& .expHeading': {
            fontStyle: 'normal',
            marginTop: 5,
            fontWeight: 600,
            fontSize: 15,
            lineHeight: '20px',
            color: '#4D5766'
        }
    },
    menuContainerItem: {
        margin: 15,
    },
    header: {
        fontSize: '16px',
        fontStyle: 'bold',
        fontWeight: 800,
    },
    menuSubContainer: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
    },
    viewAll: {
        color: '#3C7DDD',
        cursor: 'pointer',
    },
    exploreLastRow: {
        background: '#F9FAFB',
        width: '100%',
        padding: 20,
        minHeight: 340,
        borderRadius: 8,
        backgroundImage: `url(${expDevLogo})`,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        '& .expHeading': {
            fontWeight: 500,
            fontSize: 30,
            paddingTop: 60,
            lineHeight: '39px',
            color: '#4D5766'
        },
        '& .expSubHeading': {
            fontWeight: 400,
            fontSize: 16,
            paddingTop: 20,
            lineHeight: '20px',
            color: '#4D5766'
        },

    },
    browseAllBtn: {
        borderRadius: 5,
        cursor: 'pointer',
        padding: '15px 20px',
        marginTop: 40,
        marginBottom: 20
    },
    exploreIconBtn: {
        marginLeft: 20
    },
    catListMenu: {
        '& .catListMenuItem': {
            backgroundColor: 'transparent',
            padding: '2px 25px',
            fontSize: 14,
            lineHeight: '26px',
            position: 'relative',
            '& .arrowIcon': {
                position: 'absolute',
                right: 0,
                '& svg': {
                    fontSize: 14
                },

            },
            '&:hover': {
                color: theme.palette.primary.main,
                '& .arrowIcon': {
                    color: theme.palette.primary.main
                }
            },
            '&.selected': {
                color: theme.palette.primary.main,
                '& .arrowIcon': {
                    color: theme.palette.primary.main
                }
            },

        },
        '& .borderRight': {
            borderRight: '1px solid #E0E0E0'
        },
        '& .courseListMenuItem': {
            backgroundColor: 'transparent',
            padding: '2px 25px',
            fontSize: 15,
            lineHeight: '26px',
            position: 'relative',
            '&:hover': {
                color: theme.palette.primary.main,
            },
        }
    },
    popularCourseSec: {
        maxHeight: 500,
        overflowX: 'auto'
    }
}));

export const TopBarButton = ({
    name,
    icon,
    layout,
    path,
    button = false,
    activeClassName = 'active'
}) => (
    <NavLink
        to={layout + path}
        activeClassName={activeClassName}
        exact
        id="topBar_nav">
        <Button
            color={button ? 'primary' : 'secondary'}
            variant={button ? 'contained' : 'default'}
            size="large"
            id="topBar_button">
            <span id="topBar_span" ><i className={icon} /></span>
            <span> {name} </span>
        </Button>
    </NavLink>
);

export const getCourseName = (name) => {
    if (!name) {
        return "-";
    }
    name = (name.length <= 35) ? name : name.substring(0, 35).concat('...');
    return (<span dangerouslySetInnerHTML={{ __html: name }} />);
}


export const BrowseCoursePanel = ({
  courseList,
  categoryList,
  menuOpen,
  menuRef,
  handleCourseMenuClose,
  handleSubManuClicked,
  handleCategoryClick,
  selectedCategory,
  courseLoading,
}) => {
  const classes = useStyles();
  const history = useHistory();

  return (
    <Popper
      open={menuOpen}
      anchorEl={menuRef.current}
      transition
      disablePortal
      style={{ zIndex: 9999, width: '100%' }}
    >
      {({ TransitionProps }) => (
        <Grow {...TransitionProps}>
          <ClickAwayListener onClickAway={handleCourseMenuClose}>
            <Paper style={{ height: '84vh' }}>
              <div className={classes.menuContainer}>
                <Grid container spacing={2}>
                  <Grid item sm={4} md={4} lg={3}>
                    <Typography
                      className="expHeading"
                      style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}
                    >
                      <MenuBookIcon style={{ color: '#0080CA', marginRight: 5 }} />
                      Course Categories
                    </Typography>

                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        flexWrap: 'nowrap',
                        gap: 16,
                        width: '100%',
                        paddingBottom: 8,
                        overflowX: 'unset',
                        justifyContent: 'space-between',
                      }}
                    >
                      {categoryList.slice(0, 4).map((cat) => (
                        <div
                          key={cat}
                          style={{
                            border: selectedCategory === cat ? '2px solid #0080CA' : '1px solid #E0E0E0',
                            borderRadius: 16,
                            boxShadow: selectedCategory === cat
                              ? '0 6px 24px rgba(0,128,202,0.12)'
                              : '0 2px 8px rgba(0,0,0,0.06)',
                            background: selectedCategory === cat ? '#F0F8FF' : '#fff',
                            cursor: 'pointer',
                            transition: 'box-shadow 0.2s, border 0.2s',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            padding: 0,
                            minWidth: 320,
                            maxWidth: 380,
                            minHeight: 320,
                            marginBottom: 12,
                            justifyContent: 'flex-start',
                            flex: '1 1 320px',
                            overflow: 'hidden',
                          }}
                          onClick={() => handleCategoryClick(cat)}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') handleCategoryClick(cat);
                          }}
                          tabIndex={0}
                          role="button"
                        >
                          <img
                            src={expDevLogo}
                            alt={cat}
                            style={{
                              width: '100%',
                              height: 180,
                              objectFit: 'cover',
                              borderTopLeftRadius: 16,
                              borderTopRightRadius: 16,
                            }}
                          />
                          <div style={{ padding: 32, width: '100%', textAlign: 'center' }}>
                            <Typography style={{ fontWeight: 700, fontSize: 28, color: '#222' }}>
                              {cat}
                            </Typography>

                            {/* SubscriptionFlow Portal Button */}
                            <div style={{ position: 'relative' }}>
                              <a
                                id="sf-portal-link"
                                href="javascript:void(0)"
                                data-sf-type="portal"
                                data-sf-scale="full"
                                style={{ display: 'none' }}
                              >Manage account</a>
                              <button
                                type="button"
                                data-sf-type="portal"
                                data-sf-display="popup"
                                data-sf-popup-width="1000"
                                data-sf-popup-height="800"
                                style={{
                                  display: 'inline-block',
                                  marginTop: 16,
                                  padding: '10px 20px',
                                  backgroundColor: '#0080CA',
                                  color: '#fff',
                                  borderRadius: 8,
                                  textDecoration: 'none',
                                  fontWeight: 600,
                                  cursor: 'pointer',
                                  border: 'none',
                                }}
                                onClick={() => {
                                  const portalLink = document.getElementById('sf-portal-link');
                                  if (portalLink) portalLink.click();
                                }}
                              >
                                Enroll Now
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Grid>
                </Grid>
              </div>
            </Paper>
          </ClickAwayListener>
        </Grow>
      )}
    </Popper>
  );
};



const Header = () => {

    const classes = useStyles();
    const history = useHistory();
    const location = useLocation();
    const [loggedInUser, setLoggedInUser] = useState(null);
    const [open, setOpen] = React.useState(false);
    const [profileLoading, setProfileLoading] = useState(false);
    const [searchCourses, setSearchCourses] = useState([]);
    const [searchOpen, setSearchOpen] = useState(false);
    const [courseLoading, setCourseLoading] = useState(false);
    const isMobile = IsMobileDevice('sm');
    const [openCatDrawer, setOpenCatDrawer] = useState(false);
    const [haveRegCourse, setHaveRegCourse] = useState(false);
    const [regCourseLoading, setRegCourseLoading] = useState(false);
 const [searchText, setSearchText] = useState("");
  const [dropdownOpen, setDropdownOpen] = useState(false);
const [coursesDropdown, setCoursesDropdown] = useState([]);
    const anchorRef = React.useRef(null);
    const menuRef = React.useRef(null);

    const dispatch = useAuthDispatch();
    const { userDetails } = useAuthState(dispatch);
    const [menuOpen, setMenuOpen] = React.useState(false);
    const menu_items = [
        { name: "Courses", path: Paths.CourseListing },
        { name: "Plan", path: Paths.Accreditations },
        // { name: "Contact", path: `${Paths.HomePage}/#contactsec` }
    ];

    const [courseCatalogueList, setCourseCatalogueList] = useState([]);
    const [categoryList, setCategoryList] = useState([]);
    const [selectedCategory, setSelectedCategory] = useState(null);
 React.useEffect(() => {
        getCategoriesList();
        fetchAllCoursesDropdown();
        // eslint-disable-next-line
    }, []);
    // Fetch initial courses for search box
   const fetchAllCoursesDropdown = async () => {
        try {
            const response = await fetch('https://api-staging.deviare.africa/main/coursedropdownsandlist');
            const data = await response.json();
            setCoursesDropdown(data?.data?.courses || []);
        } catch (err) {
            setCoursesDropdown([]);
        }
    };
   const handleSearchInputChange = async (e) => {
    setDropdownOpen(true);
        const value = e.target.value;
        setSearchText(value);
        if (!value.trim()) {
            fetchAllCoursesDropdown();
            return;
        }
        try {
            const response = await fetch(`https://api-staging.deviare.africa/main/coursedropdownsandlist?search=${encodeURIComponent(value)}`);
            const data = await response.json();
            setCoursesDropdown(data?.data?.courses || []);
        } catch (err) {
            setCoursesDropdown([]);
        }
    };
    const getCourseCatalogue = async (category) => {
        setCourseLoading(true);
        let query = '';
        query += category ? `category=${category}&popular=true` : 'popular=true';
        const courseList = await coursesService.getEcommerceCourses(query);
        const courses = [];
        if (courseList.status) {
            courseList?.data?.map((cat) => {
                return cat?.courses?.map((itm) => {
                    courses.push({ ...itm });
                    return itm;
                });
            });
        }
        setCourseLoading(false);
        setCourseCatalogueList(courses);
    }

    const getCategoriesList = async () => {
        setCourseLoading(true);
        const categories = await coursesService.getEcommerceCategories();
        if (categories?.status) {
            setCategoryList(categories?.data?.categories || []);
            getCourseCatalogue();
        }
    }

    const handleToggleMenu = () => {
        setMenuOpen((prevOpen) => !prevOpen);
    };

    const toggleCategoryDrawer = () => {
        setOpenCatDrawer((prevOpen) => !prevOpen);
    };

    React.useEffect(() => {
        if (userDetails) {
            getUserProfile();
            getRegisterCourse();
        }
        getCategoriesList();
        //eslint-disable-next-line
    }, [userDetails]);

    const getRegisterCourse = async () => {
        setRegCourseLoading(true);
        const courseList = await skillsAndTransformation.getRegisterCourse();
        if (courseList.status) {
            setHaveRegCourse(courseList.data?.courses?.length > 0 || false);
        }
        setRegCourseLoading(false);
    }

    const getUserProfile = async () => {
        setProfileLoading(true);
        const user = await userServices.getDetailMyProfile();
        if (user && user.data && user.data.length) {
            setLoggedInUser(user.data[0]);
            localStorage.setItem('comp_ids', user.data[0].uuid);
        }
        setProfileLoading(false);
    }

    const { keycloak } = useKeycloak();

    const handleLogout = async () => {
        try {
            // Clear local storage
            localStorage.clear();

            // Clear cookies
            document.cookie.split(";").forEach(function (c) {
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
            });

            // Logout from context
            await logout(dispatch);

            // Logout from Keycloak with redirect
            keycloak.logout({
                redirectUri: window.location.origin
            });
        } catch (error) {
            console.error('Logout error:', error);
            // Fallback - force reload to home
            window.location.href = '/';
        }
    };

    const handleToggle = () => {
        setOpen((prevOpen) => !prevOpen);
    };

    const handleClose = (event) => {
        if (anchorRef.current && anchorRef.current.contains(event.target)) {
            return;
        }
        setOpen(false);
    };

    const handleProfile = () => {
        history.push(Paths.Profile);
    }

    const handleChangePassword = () => {
        history.push(Paths.ChangePassword);
    }

    function handleListKeyDown(event) {
        if (event.key === 'Tab') {
            event.preventDefault();
            setOpen(false);
        }
    }

    const handleSubManuClicked = (course) => {
        setMenuOpen(false);
        history.push((course?.uuid) ? `/course/${course?.uuid}?course_id=${course?.uuid}` : '/course-listing');
    }

    const handleCourseMenuClose = (event) => {
        if (menuRef.current && menuRef.current.contains(event.target)) {
            return;
        }
        setMenuOpen(false);
    }

    const getSearchCourseList = async (searchVal) => {
        const courseList = await coursesService.getECommerceCourses(searchVal ? `name=${searchVal}` : '');
        if (courseList.status && courseList.data) {
            const courses = [];
            courseList.data.forEach((itm) => {
                itm.courses.forEach((cs) => {
                    if (courses.length < 11) {
                        let name = cs.name.replace(/<\/?[^>]+(>|$)/g, "");
                        courses.push({ ...cs, name });
                    }
                });
            });
            setSearchCourses(courses);
        }
    }

    const handleGlobalSearch = (value) => {
        return getSearchCourseList(value);
    };

    const handleCategoryClick = (category) => {
        if (selectedCategory === category) {
            return null;
        }
        setSelectedCategory(category);
        return getCourseCatalogue(category);
    }

    // Utility to trigger the portal/modal for managing account
    function openManageAccountPortal() {
        // If you have a custom portal/modal, call it here
        // If you rely on a third-party script, trigger it here
        // Example: window.openPortal && window.openPortal();
        // If you need to trigger a click on a hidden element, do it here
        const portalLink = document.querySelector('a[data-sf-type="portal"]');
        if (portalLink) {
            portalLink.click();
        } else {
            // Optionally, create and click the link if not present
            const link = document.createElement('a');
            link.setAttribute('data-sf-type', 'portal');
            link.setAttribute('data-sf-scale', 'full');
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    const handleEnrollNow = React.useCallback(() => {
        // Try to find an existing portal link and click it
        const portalLink = document.querySelector('a[data-sf-type="portal"]');
        if (portalLink) {
            portalLink.click();
            console.log('Clicked existing portal link');
        } else {
            // Create a hidden anchor element with the required attributes
            const link = document.createElement('a');
            link.setAttribute('data-sf-type', 'portal');
            link.setAttribute('data-sf-scale', 'full');
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            console.log('Created and clicked new portal link');
        }
    }, []);

    return (
    <div className={classes.root}>
            <AppBar
                position="static"
                className={classes.headerBar}
            >
                <Toolbar>
                    <div style={{ margin: 'auto' }}>
                        {(isMobile) ?
                            <img
                                style={{ cursor: "pointer", paddingTop: 8, width: 160, maxHeight: 85 }}
                                alt="Deviare"
                                onClick={() => history.push(Paths.HomePage)}
                                src={logoImg}
                            /> :
                            <img
                                style={{ cursor: "pointer", paddingTop: 8, width: 60, maxHeight: 80 }}
                                alt="Deviare"
                                onClick={() => history.push(Paths.HomePage)}
                                src={mobileLogo}
                            />
                        }
                    </div>
                    <div className={classes.mainMenuItem}>
                        {/* {(isMobile) &&
                            <Button
                                style={{ color: (location.pathname.startsWith(Paths.CourseListing)) ? '#0080CA' : '#4D5766', fontSize: 16 }}
                                ref={menuRef}
                                aria-controls={menuOpen ? 'menu-list-grow' : undefined}
                                aria-haspopup="true"
                                onClick={handleToggleMenu}
                            >
                                <MenuBookIcon style={{ color: '#0080CA', marginRight: 10 }} /> Courses
                            </Button>
                        } */}
                        {menu_items.map((menuItem) => (
                            <Button
                                style={{ color: (location.pathname.startsWith(menuItem.path)) ? '#0080CA' : '#4D5766', fontSize: 16 }}
                                onClick={() => history.push(menuItem.path)}
                            >
                                {menuItem.name}
                            </Button>
                        ))}
                    </div>
                    <div className={classes.rightMenuItem}>
                        <div className={classes.searchBar}>
                            {(isMobile) && <>
                                 <div style={{ position: 'relative', width: 200 }}>
                                    <input
                                        type="text"
                                        value={searchText}
                                        onChange={handleSearchInputChange}
                                        onFocus={() => setDropdownOpen(true)}
                                        onBlur={() => setTimeout(() => setDropdownOpen(false), 150)}
                                        placeholder="Search courses..."
                                        style={{ padding: '10px', borderRadius: 4, border: '1px solid #ccc', width: '100%' }}
                                    />
                                    {(dropdownOpen && coursesDropdown.length > 0) && (
                                        <div style={{ background: '#fff', border: '1px solid #eee', marginTop: 2, borderRadius: 4, maxHeight: 300, overflowY: 'auto', width: '100%', position: 'absolute', zIndex: 10 }}>
                                            {coursesDropdown.map((course) => (
                                                <div key={course.uuid} style={{ padding: '10px', borderBottom: '1px solid #eee', cursor: 'pointer' }}
                                                    onMouseDown={() => { setDropdownOpen(false); history.push(`/course/${course?.uuid}?course_id=${course?.uuid}`); }}>
                                                    {course.name}
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            
{/*  Start Learning Button - visible only if user is logged in */}
{userDetails && (
  <Button
    variant="contained"
    color="primary"
    style={{ marginLeft: 12, padding: '12px 20px', borderRadius: 4 }}
    onClick={() => history.push('/account/courses')}
  >
   Start Learning
  </Button>
)}

                                {/* {!regCourseLoading &&
                                    <Button
                                        variant="outlined"
                                        color="primary"
                                        onClick={() => history.push(haveRegCourse ? Paths.RegisteredCourses : Paths.CourseListing)}
                                        aria-haspopup="true"
                                        style={{ padding: '12px 20px', borderRadius: 4 }}>
                                        {haveRegCourse ? "Start Learning" : "Get Started"}
                                    </Button>
                                } */}
                            </>
                            }
                            <IconButton className={classes.iconBtn} aria-label="show 17 new notifications">
                                <Avatar alt={loggedInUser?.firstName} src={profileLoading ? userImg : loggedInUser?.profile_image || '/userImage.jpg'} className={classes.largeImg} />
                                {(!isMobile) && <ExpandMoreIcon ref={anchorRef} onClick={handleToggle} />}
                            </IconButton>

                            {(!isMobile) &&
                                <IconButton className={classes.exploreIconBtn} onClick={toggleCategoryDrawer}>
                                    <DehazeIcon color="primary" />
                                </IconButton>
                            }
                            {(isMobile) &&
                                <Button
                                    ref={anchorRef}
                                    aria-controls={open ? 'menu-list-grow' : undefined}
                                    aria-haspopup="true"
                                    style={{ color: variables.darkThemeText }}
                                    onClick={handleToggle}
                                >
                                    {(loggedInUser) ? `${loggedInUser.firstName}` : "Profile"} <ExpandMoreIcon />
                                </Button>
                            }
                        </div>
                    </div>
                </Toolbar>
            </AppBar>
            {(isMobile) && <BrowseCoursePanel
                menuOpen={menuOpen}
                handleCategoryClick={handleCategoryClick}
                selectedCategory={selectedCategory}
                categoryList={categoryList}
                courseLoading={courseLoading}
                courseList={courseCatalogueList}
                handleSubManuClicked={handleSubManuClicked}
                handleCourseMenuClose={handleCourseMenuClose}
                menuRef={menuRef}
                handleEnrollNow={handleEnrollNow}
            />}
            <Popper open={open} anchorEl={anchorRef.current} transition disablePortal style={{ zIndex: 1 }}>
                {({ TransitionProps, placement }) => (
                    <Grow style={{ width: "197px", backgroundColor: variables.headerColor, marginTop: 34, transformOrigin: placement === 'bottom' ? 'center top' : 'center bottom' }} {...TransitionProps} >
                        <Paper>
                            <div style={{ width: "100%", padding: 10, borderBottom: "1px solid #DEDEDE" }}>
                                <div style={{ position: "relative" }} className={classes.profileImg}>
                                    <img alt='img' width={'80px'} height={'80px'} src={profileLoading ? userImg : loggedInUser && loggedInUser.profile_image ? loggedInUser.profile_image : defaultImage} className={classes.largeImg} />
                                    <span className={classes.editBack}><EditIcon style={{ fontSize: 16, fontWeight: "bold", cursor: "pointer" }} /></span>
                                </div>
                            </div>
                            <ClickAwayListener onClickAway={handleClose}>
                                <MenuList autoFocusItem={open} id="menu-list-grow" onKeyDown={handleListKeyDown}>
                                    <MenuItem style={{ color: variables.darkThemeText }} onClick={(evt) => history.push('/account/courses')}>Learning</MenuItem>
                                    <MenuItem style={{ color: variables.darkThemeText }} onClick={handleProfile}>Profile</MenuItem>
                                    <MenuItem style={{ color: variables.darkThemeText }} onClick={handleChangePassword}>Change Password</MenuItem>
                                    <MenuItem style={{ color: variables.darkThemeText }} onClick={handleLogout}>Logout</MenuItem>
                                </MenuList>
                            </ClickAwayListener>
                        </Paper>
                    </Grow>
                )}
            </Popper>
            {(!isMobile) && <>
                <CategoryDrawer
                    handleViewAllClick={(cat) => {
                        history.push(getEncodeURL(`${Paths.CourseListing}?category=${cat}`))
                    }}
                    isAuth={true}
                    open={openCatDrawer}
                    list={categoryList}
                    toggleDrawer={toggleCategoryDrawer}
                />
            </>
            }
        </div>
    );
};

export default Header;