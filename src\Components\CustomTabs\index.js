import React, { useEffect, useState } from 'react';
import SwipeableViews from 'react-swipeable-views';
import { makeStyles, useTheme } from '@material-ui/core/styles';
import AppBar from '@material-ui/core/AppBar';
import Tabs from '@material-ui/core/Tabs';
import Tab from '@material-ui/core/Tab';
import TabPanel from '../TabPanel';
import PropTypes from 'prop-types';
import './tabStyle.css'

const useStyles = makeStyles(theme => ({
    tabAppbar: {
        borderBottom: 'none !important',
        overflowX: 'auto'
    },
    tabStyles: {
        color: '#0080CA',
        backgroundColor: '#F2F2F2',
        minWidth: 185

    },
    scrollButtons: {
        color: 'black',
        overflowY: 'auto'
        // borderBottom: '1px solid rgb(222, 222, 222)'
    }
}));
export default function CustomTabs({ tabs, 
    activeIndex, 
    handleChangeActiveNotiTab, 
    customClass, 
    handleSetActiveIndex,
    headingStyle 
}) {
    const [value, setValue] = useState(0);
    const classes = useStyles();
    const theme = useTheme();
    useEffect(() => {
        if (activeIndex) {
            setValue(activeIndex);
        }
    }, [activeIndex]);
    const handleChange = (event, newValue) => {
        setValue(newValue);
        handleSetActiveIndex && handleSetActiveIndex(newValue);
        if (typeof handleChangeActiveNotiTab !== "undefined") {
            handleChangeActiveNotiTab(false, newValue);
        }
    };
    return (
        <>
            <AppBar style={(headingStyle) ? headingStyle : null} position="static" color="transparent" className={classes.tabAppbar} elevation={0}>
                <Tabs
                    value={value}
                    onChange={handleChange}
                    variant="scrollable"
                    scrollButtons="auto"
                    className={customClass || classes.scrollButtons}
                    textColor="primary">
                    {tabs.length &&
                        tabs.map((tab, tabIndex) => (
                            <Tab
                                label={tab.label}
                                id={`tab-${tabIndex}`}
                                key={tabIndex}
                                className={classes.tabStyles}
                                color={'primary'}
                            />
                        ))}
                </Tabs>
            </AppBar>
            <SwipeableViews
                axis={theme.direction === 'rtl' ? 'x-reverse' : 'x'}
                index={value}
                onChangeIndex={handleChange}>
                {tabs.length &&
                    tabs.map((tab, tabIndex) => (
                        <TabPanel
                            marginTop={tab?.marginTop || null}
                            value={value}
                            index={tabIndex}
                            dir={theme.direction}
                            key={tabIndex}>
                            {tab.children}
                        </TabPanel>
                    ))}
            </SwipeableViews>
        </>
    );
}
CustomTabs.defaultProps = {
    activeIndex: 0
};
CustomTabs.propTypes = {
    tabs: PropTypes.array.isRequired,
    activeIndex: PropTypes.number
};
