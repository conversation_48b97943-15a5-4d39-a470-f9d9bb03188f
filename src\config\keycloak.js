import Keycloak from 'keycloak-js';

const keycloakConfig = {
    url: process.env.REACT_APP_KEYCLOAK_URL,
    realm: process.env.REACT_APP_KEYCLOAK_REALM,
    clientId: process.env.REACT_APP_KEYCLOAK_CLIENT_ID
};

export const initOptions = {
    checkLoginIframe: false,
    pkceMethod: 'S256',
    enableLogging: true,
    flow: 'standard',
    // onLoad: 'login-required', // Changed to ensure authentication
    silentCheckSsoFallback: false,
    token: localStorage.getItem('SSO'),
    refreshToken: localStorage.getItem('refreshToken'),
    idToken: localStorage.getItem('ID:SSOToken'),
    timeSkew: 0,
    responseMode: 'fragment'
};

// Validate required environment variables
if (!keycloakConfig.url || !keycloakConfig.realm || !keycloakConfig.clientId) {
    console.error('Missing required Keycloak configuration in environment variables');
    throw new Error('Missing required Keycloak configuration in environment variables');
}

export const keycloak = new Keycloak(keycloakConfig);

// Initialize token refresh
keycloak.onTokenExpired = () => {
    console.log('Token expired, attempting refresh...');
    keycloak.updateToken(30).then((refreshed) => {
        if (refreshed) {
            console.log('Token refreshed');
            localStorage.setItem('SSO', keycloak.token);
            localStorage.setItem('refreshToken', keycloak.refreshToken);
            localStorage.setItem('ID:SSOToken', keycloak.idToken);
        }
    }).catch(error => {
        console.error('Failed to refresh token:', error);
        window.location.reload();
    });
};
