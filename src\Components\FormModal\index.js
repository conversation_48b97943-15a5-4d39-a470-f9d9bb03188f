import React from 'react';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import CircularProgress from '@material-ui/core/CircularProgress';

function ButtonComponent(props) {
    const { onClick, loading } = props;
    return (
        <Button
            color="primary"
            style={{
              borderRadius: 5
            }}
            variant={'contained'}
            onClick={onClick} disabled={loading}>
            {loading && <CircularProgress size={14} />}
            {!loading && "Save"}
        </Button>
    );
}

export default function FormModal({open, handleClose, handleSubmit, title, children, loading}) {
  
  return (
    <div>
      <Dialog fullWidth={true} maxWidth={"md"} open={open} onClose={handleClose} aria-labelledby="form-dialog-title">
        <DialogTitle id="form-dialog-title">{title}</DialogTitle>
        <DialogContent>
          {children}
        </DialogContent>
        <DialogActions>
          <Button style={{
            background: "#B7B7B7",
            padding: 10,
            color: "#fff",
            marginRight: 20,
            borderRadius: 5
          }} onClick={handleClose} color="primary">
            Cancel
          </Button>
          <ButtonComponent onClick={handleSubmit} loading={loading}  />
        </DialogActions>
      </Dialog>
    </div>
  );
}
