import { Swiper, SwiperSlide } from 'swiper/react';
import SwiperCore, { A11y, Keyboard, Navigation } from 'swiper';
import 'swiper/css';
import { Divider, IconButton, Paper, Typography, makeStyles } from '@material-ui/core';
import { ArrowBackIos, ArrowForwardIos } from '@material-ui/icons';
import './Carousel.scss';
import { useRef } from 'react';
import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';
import CardMedia from '@material-ui/core/CardMedia';
import { getCourseName } from 'Components/layouts/Header';
import { IsMobileDevice } from 'utils';

const FOCUSABLE_ELEMENT_SELECTORS = [
  'a:not([disabled])',
  'button:not([disabled])',
  'input[type=text]:not([disabled])',
  '[tabindex]:not((Idisabled]):not([tabindexe"-1"))'
];

SwiperCore.use([Navigation, Keyboard, A11y]);
let swiperCounter = 1;

const useStyles = makeStyles({
  root: {
    maxWidth: 450,
    minHeight: 370,
    maxHeight: 450
  },
  brnadLogoCard: {
    background: "none",
    boxShadow: 'none',
    minHeight: 150,
  },
  media: {
    height: 150
  },
  cover: {
    width: "100%",
    maxHeight: 100,
    borderRadius: 8
  }
});

export const handleSwiperSlides = (data) => {
  const { slides, visibleSlides } = data || {};
  if (!slides || !visibleSlides) {
    return null;
  }
  slides.forEach((item) => {
    item.setAttribute('aria-hidden', 'true');
    const focusableElements = item.querySelectorAll(...FOCUSABLE_ELEMENT_SELECTORS);
    focusableElements.forEach((item) => item.setAttribute('tabindex', '-1'));
  });
  visibleSlides.forEach((item) => {
    item.setAttribute('aria-hidden');
    const focusableElements = item.querySelectorAll(...FOCUSABLE_ELEMENT_SELECTORS);
    focusableElements.forEach((item) => item.setAttribute('tabindex'));
  });
}

const ProductCard = ({ itm, handleClickItem, type = null }) => {
  const classes = useStyles();
  return (type === 'brandLogo') ?
      (<Card className={classes.brnadLogoCard}>
          <img src={itm} className={classes.cover} alt="Deviare logo" />
        </Card>)
      :
      (<Paper style={{ margin: 15 }}>
        <Card className={classes.root}>
          <CardMedia
            onClick={(evt) => handleClickItem ? handleClickItem(itm) : null}
            className={classes.media}
            image={itm.image}
            title="Contemplative Reptile"
          />
          <CardContent>
            <Typography style={{ paddingBottom: 10 }} color="primary" onClick={(evt) => handleClickItem ? handleClickItem(itm) : null} variant="h4">
              {getCourseName(itm.name)}
            </Typography>
            <Divider />
            <Typography style={{ paddingTop: 10 }} title={itm?.fullDesc} variant="body2" color="textSecondary" component="p">
              {itm.desc}
            </Typography>
          </CardContent>
        </Card>
      </Paper>)
}

export default function Carousel({ list, handleClickItem, type }) {

  swiperCounter += 1;
  const carouselRef = useRef();
  const isMobile = IsMobileDevice('sm');
  const slidesPerView = (type === 'brandLogo') ? (isMobile) ? 6 : 3 : 4;

  return (
    <div className='Product__Carousel'>
      <div style={(!isMobile) ? {display: 'none'} : null} className={`Product__Navigation${type === 'brandLogo' ? '_Brand' : ''} Product__Navigation-${swiperCounter}`}>
        <IconButton className='Product__Navigation__button Product__Navigation__button--back'>
          <ArrowBackIos />
        </IconButton>
        <IconButton className='Product__Navigation__button Product__Navigation__button--forward'>
          <ArrowForwardIos />
        </IconButton>
      </div>
      <Swiper
        ref={carouselRef}
        freeMode={{
          minimumVelocity: 0.01,
          momentum: true,
          momentumBounce: true,
          momentumBounceRatio: 1.2,
          momentunRatio: 1.2,
          momentumVelocityRatio: 1.2,
          sticky: true
        }}
        watchoverflow={true}
        watchSLidesProgress={true}
        watchSlidesVisibility={true}
        preloadImages={true}
        spaceBetween={(slidesPerView === 4) ? 20 : 40}
        navigation={{
          prevEl: `.Product__Navigation-${swiperCounter} .Product__Navigation__button--back`,
          nextEl: `.Product__Navigation-${swiperCounter} .Product__Navigation__button--forward`,
          disabledClass: `Product__Navigation__button--disabled`,
          hiddenClass: `Product__Navigation__button--hidden`
        }}
        onSwiper={(swiper) => handleSwiperSlides(swiper)}
        onSlideChange={(swiper) => handleSwiperSlides(swiper)}
        breakpoints={{
          1200: {
            slidesPerView: slidesPerView,
            slidesPerGroup: slidesPerView
          },
          992: {
            slidesPerView: slidesPerView,
            slidesPerGroup: slidesPerView
          },
          768: {
            slidesPerView: 2,
            slidesPerGroup: 2
          }
        }}
        grid={{
          rows: 3
        }}
        speed={600}
        slidesPerView={slidesPerView}
        className="mySwiper"
      >
        {list.map((itm, key) => (<>
          <SwiperSlide key={key} >
            <ProductCard type={type} handleClickItem={() => handleClickItem(itm)} itm={itm} />
          </SwiperSlide>
        </>
        ))}
      </Swiper>
    </div>
  );
}
