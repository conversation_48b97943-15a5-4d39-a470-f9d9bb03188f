import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Box from '@material-ui/core/Box';
import Card from '@material-ui/core/Card';
import Typography from '@material-ui/core/Typography';
import TextField from '@material-ui/core/TextField';
import Button from '@material-ui/core/Button';
import MenuItem from '@material-ui/core/MenuItem';
import Select from '@material-ui/core/Select';
import FormControl from '@material-ui/core/FormControl';
import InputLabel from '@material-ui/core/InputLabel';
import CircularProgress from '@material-ui/core/CircularProgress';
import Snackbar from '@material-ui/core/Snackbar';
import Alert from '@material-ui/lab/Alert';
import Layout from "../../Components/layouts";
import axios from 'axios';

const reasons = [
    "It's too expensive",
    "It's not what I expected", 
    "I don't have time",
    "It's missing features",
    "I found an alternative",
    "Other (Please Specify)"
];

const useStyles = makeStyles((theme) => ({
    root: {
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f7f7fa',
        padding: theme.spacing(2),
    },
    card: {
        width: '100%',
        maxWidth: 650,
        padding: theme.spacing(4),
        borderRadius: 16,
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e0e0e0',
    },
    title: {
        fontWeight: 700,
        fontSize: '1.5rem',
        marginBottom: theme.spacing(2),
        color: '#1a1a1a',
    },
    subtitle: {
        fontWeight: 600,
        fontSize: '1rem',
        marginBottom: theme.spacing(1),
        color: '#1a1a1a',
    },
    description: {
        color: '#757575',
        marginBottom: theme.spacing(3),
        lineHeight: 1.5,
    },
    sectionTitle: {
        fontWeight: 600,
        fontSize: '0.95rem',
        marginBottom: theme.spacing(2),
        color: '#1a1a1a',
    },
    feedbackField: {
        marginBottom: theme.spacing(3),
        '& .MuiOutlinedInput-root': {
            backgroundColor: '#f5f5f5',
            borderRadius: 8,
            '& fieldset': {
                borderColor: '#e0e0e0',
            },
            '&:hover fieldset': {
                borderColor: '#bdbdbd',
            },
            '&.Mui-focused fieldset': {
                borderColor: '#1976d2',
                borderWidth: 2,
            },
        },
    },
    selectContainer: {
        marginBottom: theme.spacing(2),
        width: '100%',
    },
    select: {
        '& .MuiOutlinedInput-root': {
            backgroundColor: '#fff',
            borderRadius: 8,
            '& fieldset': {
                borderColor: '#e0e0e0',
            },
            '&:hover fieldset': {
                borderColor: '#bdbdbd',
            },
            '&.Mui-focused fieldset': {
                borderColor: '#1976d2',
                borderWidth: 2,
            },
        },
        '& .MuiInputLabel-root': {
            color: '#757575',
            fontSize: '1rem',
            '&.Mui-focused': {
                color: '#1976d2',
            },
            '&.MuiInputLabel-shrink': {
                fontSize: '0.75rem',
                transform: 'translate(14px, -9px) scale(0.75)',
            },
        },
        '& .MuiSelect-select': {
            padding: '16.5px 14px',
            fontSize: '1rem',
            color: '#1a1a1a',
            '&:focus': {
                backgroundColor: 'transparent',
            },
        },
        '& .MuiSelect-icon': {
            color: '#757575',
        },
        '& .MuiInput-underline:before': {
            display: 'none',
        },
        '& .MuiInput-underline:after': {
            display: 'none',
        },
        '& .MuiInput-underline:hover:not(.Mui-disabled):before': {
            display: 'none',
        },
    },
    additionalField: {
        marginBottom: theme.spacing(3),
        '& .MuiOutlinedInput-root': {
            backgroundColor: '#f5f5f5',
            borderRadius: 8,
            '& fieldset': {
                borderColor: '#e0e0e0',
            },
            '&:hover fieldset': {
                borderColor: '#bdbdbd',
            },
            '&.Mui-focused fieldset': {
                borderColor: '#1976d2',
                borderWidth: 2,
            },
        },
    },
    buttonContainer: {
        display: 'flex',
        justifyContent: 'space-between',
        gap: theme.spacing(2),
        marginTop: theme.spacing(3),
    },
    stayButton: {
        borderRadius: 25,
        paddingLeft: theme.spacing(3),
        paddingRight: theme.spacing(3),
        paddingTop: theme.spacing(1.5),
        paddingBottom: theme.spacing(1.5),
        textTransform: 'none',
        fontWeight: 500,
        borderColor: '#1976d2',
        color: '#1976d2',
        '&:hover': {
            borderColor: '#1565c0',
            backgroundColor: 'rgba(25, 118, 210, 0.04)',
        },
        '&:disabled': {
            borderColor: '#ccc',
            color: '#ccc',
        },
    },
    cancelButton: {
        borderRadius: 25,
        paddingLeft: theme.spacing(3),
        paddingRight: theme.spacing(3),
        paddingTop: theme.spacing(1.5),
        paddingBottom: theme.spacing(1.5),
        textTransform: 'none',
        fontWeight: 500,
        backgroundColor: '#1976d2',
        '&:hover': {
            backgroundColor: '#1565c0',
        },
        '&:disabled': {
            backgroundColor: '#ccc',
        },
    },
    loadingContainer: {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(1),
    },
    otherInput: {
        marginTop: theme.spacing(2),
        '& .MuiOutlinedInput-root': {
            backgroundColor: '#f5f5f5',
            borderRadius: 8,
            '& fieldset': {
                borderColor: '#e0e0e0',
            },
            '&:hover fieldset': {
                borderColor: '#bdbdbd',
            },
            '&.Mui-focused fieldset': {
                borderColor: '#1976d2',
                borderWidth: 2,
            },
        },
    },
    subscriptionInfo: {
        backgroundColor: '#e3f2fd',
        padding: theme.spacing(2),
        borderRadius: 8,
        marginBottom: theme.spacing(3),
        border: '1px solid #bbdefb',
    },
    subscriptionText: {
        fontSize: '0.9rem',
        color: '#1565c0',
        fontWeight: 500,
    },
    loadingCard: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: theme.spacing(2),
        padding: theme.spacing(4),
    },
}));

const CancelSubscription = () => {
    const classes = useStyles();
    const [feedback, setFeedback] = useState('');
    const [reason, setReason] = useState('');
    const [otherReason, setOtherReason] = useState('');
    const [additional, setAdditional] = useState('');
    const [loading, setLoading] = useState(false);
    const [initialLoading, setInitialLoading] = useState(true);
    const [subscriptions, setSubscriptions] = useState([]);
    const [activeSubscriptionId, setActiveSubscriptionId] = useState(null);
    const [notification, setNotification] = useState({
        open: false,
        message: '',
        severity: 'success'
    });

    // Get UUID from localStorage for fetching order subscription
    const userId = localStorage.getItem('uuid') || '672d26a6-8bce-4450-80ff-8591e15d1c18';

    // Fetch order subscription data on component mount
    useEffect(() => {
        fetchOrderSubscription();
    }, []);

    const fetchOrderSubscription = async () => {
        try {
            setInitialLoading(true);
            console.log('Fetching order subscription for user:', userId);
            
            const response = await axios.get(
                `https://api-staging.deviare.africa/main/orderSubscription/${userId}`,
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'accept': 'application/json, text/plain, */*',
                    },
                    timeout: 15000,
                }
            );

            console.log('Order subscription response:', response.data);

            if (response.data.success && response.data.data) {
                const confirmedSubs = response.data.data.confirmed_subscriptions || [];
                setSubscriptions(confirmedSubs);
                
                if (confirmedSubs.length > 0) {
                    // Get the last element (most recent subscription)
                    const lastSubscription = confirmedSubs[confirmedSubs.length - 1];
                    setActiveSubscriptionId(lastSubscription.id);
                    console.log('Active subscription ID for cancellation:', lastSubscription.id);
                } else {
                    setNotification({
                        open: true,
                        message: 'No active subscriptions found to cancel.',
                        severity: 'warning'
                    });
                }
            } else {
                setNotification({
                    open: true,
                    message: 'Failed to load subscription information.',
                    severity: 'error'
                });
            }
        } catch (error) {
            console.error('Error fetching order subscription:', error);
            setNotification({
                open: true,
                message: 'Failed to load subscription information.',
                severity: 'error'
            });
        } finally {
            setInitialLoading(false);
        }
    };

    const handleCloseNotification = () => {
        setNotification({ ...notification, open: false });
    };

    const handleCancelSubscription = async () => {
        // Check if we have an active subscription ID
        if (!activeSubscriptionId) {
            setNotification({
                open: true,
                message: 'No active subscription found to cancel.',
                severity: 'error'
            });
            return;
        }

        // Basic validation
        if (!reason.trim()) {
            setNotification({
                open: true,
                message: 'Please select a reason for cancellation',
                severity: 'error'
            });
            return;
        }

        // If "Other" is selected, check for otherReason
        if (reason === "Other (Please Specify)" && !otherReason.trim()) {
            setNotification({
                open: true,
                message: 'Please specify your reason for cancellation',
                severity: 'error'
            });
            return;
        }

        setLoading(true);

        try {
            const finalReason = reason === "Other (Please Specify)" ? otherReason : reason;
            
            // Payload matching your exact curl request
            const payload = {
                cancellation_option: "Today",
                feedback: feedback.trim() || "Service not meeting expectations",
                reason: finalReason,
                additional: additional.trim() || "Will consider returning in future"
            };

            console.log('Cancelling subscription with ID:', activeSubscriptionId);
            console.log('Cancel payload:', payload);

            // API call using subscription ID in URL instead of user ID
            const response = await axios.post(
                `https://api-staging.deviare.africa/main/CancelSubscription/6257f901-df3e-42b5-ba13-9c543f583bc8/`,
                payload,
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'accept': 'application/json, text/plain, */*',
                    },
                    timeout: 15000,
                }
            );

            console.log('Cancel subscription response:', response.data);

            if (response.status === 200 || response.status === 201) {
                setNotification({
                    open: true,
                    message: 'Subscription cancelled successfully',
                    severity: 'success'
                });
                
                // Clear form after success
                setFeedback('');
                setReason('');
                setOtherReason('');
                setAdditional('');
                
                // Optional: Redirect user after successful cancellation
                setTimeout(() => {
                    window.location.href = '/';
                }, 3000);
            }

        } catch (error) {
            console.error('Cancel subscription error:', error);
            
            let errorMessage = 'Failed to cancel subscription. Please try again.';
            
            if (error.response) {
                console.log('Error response:', error.response.data);
                errorMessage = error.response.data?.message || 
                              error.response.data?.error ||
                              `Server error: ${error.response.status}`;
            } else if (error.request) {
                errorMessage = 'Network error. Please check your connection.';
            }

            setNotification({
                open: true,
                message: errorMessage,
                severity: 'error'
            });
        } finally {
            setLoading(false);
        }
    };

    const handleStaySubscribed = () => {
        setNotification({
            open: true,
            message: 'Great! Your subscription remains active.',
            severity: 'success'
        });
        
        setTimeout(() => {
            window.location.href = '/dashboard';
        }, 1500);
    };

    const handleReasonChange = (e) => {
        setReason(e.target.value);
        if (e.target.value !== "Other (Please Specify)") {
            setOtherReason('');
        }
    };

    // Show loading while fetching subscription data
    if (initialLoading) {
        return (
            <Layout>
                <Box className={classes.root}>
                    <Card className={classes.card}>
                        <div className={classes.loadingCard}>
                            <CircularProgress size={50} />
                            <Typography>Loading subscription information...</Typography>
                        </div>
                    </Card>
                </Box>
            </Layout>
        );
    }

    return (
        <Layout>
            <Box className={classes.root}>
                <Card className={classes.card}>
                    <Typography className={classes.title}>
                        Sorry to see you go
                    </Typography>
                    
                    <Typography className={classes.subtitle}>
                        You're about to cancel your subscription
                    </Typography>
                    
                    <Typography className={classes.description}>
                        Once your subscription expires on your next invoice date, you'll lose access to Red Breeze.
                    </Typography>

                    {/* Show current subscription info */}
                    {subscriptions.length > 0 && (
                        <div className={classes.subscriptionInfo}>
                            <Typography className={classes.subscriptionText}>
                                Active Subscription: {subscriptions[subscriptions.length - 1].display_name}
                            </Typography>
                            <Typography className={classes.subscriptionText} style={{ fontSize: '0.8rem', marginTop: 4 }}>
                                Subscription ID: {subscriptions[subscriptions.length - 1].id}
                            </Typography>
                        </div>
                    )}

                    <Typography className={classes.sectionTitle}>
                        You wanted to do this when you signed up
                    </Typography>
                    
                    <TextField
                        multiline
                        rows={4}
                        fullWidth
                        placeholder="Share your feedback here"
                        variant="outlined"
                        value={feedback}
                        onChange={e => setFeedback(e.target.value)}
                        className={classes.feedbackField}
                        disabled={loading}
                    />

                    <Typography className={classes.sectionTitle}>
                        What is your reason for cancelling?
                    </Typography>
                    
                    <FormControl fullWidth className={classes.selectContainer}>
                        <InputLabel id="reason-select-label">Select a reason</InputLabel>
                        <Select
                            labelId="reason-select-label"
                            value={reason}
                            onChange={handleReasonChange}
                            label="Select a reason"
                            className={classes.select}
                            disabled={loading}
                            variant="outlined"
                        >
                            {reasons.map((r, i) => (
                                <MenuItem key={i} value={r}>{r}</MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    {/* Show additional input field when "Other" is selected */}
                    {reason === "Other (Please Specify)" && (
                        <TextField
                            fullWidth
                            placeholder="Please specify your reason"
                            variant="outlined"
                            value={otherReason}
                            onChange={e => setOtherReason(e.target.value)}
                            className={classes.otherInput}
                            disabled={loading}
                        />
                    )}
                    
                    <TextField
                        multiline
                        rows={4}
                        fullWidth
                        placeholder="Additional comments"
                        variant="outlined"
                        value={additional}
                        onChange={e => setAdditional(e.target.value)}
                        className={classes.additionalField}
                        disabled={loading}
                        style={{ marginTop: reason === "Other (Please Specify)" ? 16 : 0 }}
                    />

                    <Box className={classes.buttonContainer}>
                        <Button 
                            variant="outlined" 
                            className={classes.stayButton}
                            size="large"
                            onClick={handleStaySubscribed}
                            disabled={loading}
                        >
                            Stay subscribed
                        </Button>
                        <Button 
                            variant="contained" 
                            color="primary" 
                            className={classes.cancelButton}
                            size="large"
                            onClick={handleCancelSubscription}
                            disabled={loading || !activeSubscriptionId}
                        >
                            <div className={classes.loadingContainer}>
                                {loading && <CircularProgress size={20} color="inherit" />}
                                Cancel Subscription
                            </div>
                        </Button>
                    </Box>
                </Card>

                <Snackbar
                    open={notification.open}
                    autoHideDuration={6000}
                    onClose={handleCloseNotification}
                    anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
                >
                    <Alert onClose={handleCloseNotification} severity={notification.severity}>
                        {notification.message}
                    </Alert>
                </Snackbar>
            </Box>
        </Layout>
    );
};

export default CancelSubscription;
