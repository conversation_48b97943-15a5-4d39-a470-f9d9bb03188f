import React from 'react';
import {Text, View, StyleSheet } from '@react-pdf/renderer';
const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        color: '#62A3CD',
        backgroundColor: '#DCE9F1',
        height: 24,
        paddingTop: 5,
        paddingLeft: 5,
        marginTop: 10,
        marginBottom: 10,
        fontStyle: 'bold',
        fontSize: 12,
        flexGrow: 1,
    },
    description: {
        width: '25%'
    },
    head1: {
        width: '25%',
        textAlign: "center"
    },
    qty: {
        width: '10%',
        textAlign: "center"
    },
    tax: {
        width: '20%',
        textAlign: "center"
    },
    rate: {
        width: '20%',
        paddingRight: 10,
        textAlign: "right"
    }
  });

  const InvoiceTableHeader = ({row}) => {
      return (
        <View style={styles.container}>
            <Text style={styles.description}>{row.title}</Text>
            <Text style={styles.head1}>{row.head1}</Text>
            <Text style={styles.tax}>{row.head3}</Text>
            <Text style={styles.qty}>{row.head4}</Text>
            <Text style={styles.rate}>{row.head5}</Text>
        </View>
    );
}
  
  export default InvoiceTableHeader