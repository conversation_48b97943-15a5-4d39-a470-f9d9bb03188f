import React from 'react';
import { ReactKeycloakProvider } from '@react-keycloak/web';
import { keycloak, initOptions } from '../config/keycloak';
import CircularProgress from '@material-ui/core/CircularProgress';
import { useAuthDispatch } from './context';
import { authorizeWithSSO } from 'Services/AuthService';

const LoadingComponent = () => (
  <div style={{
    position: 'fixed',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)'
  }}>
    <CircularProgress />
  </div>
);

export const KeycloakProvider = ({ children }) => {
  const [isLoading, setIsLoading] = React.useState(true);
  const dispatch = useAuthDispatch();

  // Remove the problematic useEffect that was causing continuous reloads
  // Instead, handle auth state changes through Keycloak events

  const handleAuthentication = async (authenticated) => {
    if (authenticated) {
      const hasRefreshed = localStorage.getItem("hasRefreshed");

      if (!hasRefreshed) {
        try {
          setIsLoading(true);
          // Store Keycloak tokens
          console.log("Storing Keycloak tokens...",keycloak);
          localStorage.setItem("SSO", keycloak?.token);
          localStorage.setItem("ID:SSOToken", keycloak?.idToken);
          localStorage.setItem("session_state", keycloak?.tokenParsed?.session_state);
          localStorage.setItem("isLogin", true);
          localStorage.setItem("user", keycloak?.tokenParsed?.preferred_username);
          localStorage.setItem("refreshToken", keycloak?.refreshToken);
          localStorage.setItem("firstname", keycloak?.given_name);
          localStorage.setItem("lastname", keycloak?.family_name);
          localStorage.setItem("userid", keycloak?.sub);
          // Call the simplified auth function
          const response = await authorizeWithSSO(keycloak?.token);
          const { data } = response;

          // Rest of your authentication logic...
          dispatch({ type: 'LOGIN_SUCCESS', payload: data });

          // Store user data
          const user_auth = {
            ...data,
            isLogin: true,
            user: keycloak?.tokenParsed?.preferred_username,
            company_id: data.customers ? data.customers[0] : null,
            company_logo: data.company_logo || null,
          };

          // Store all required data in localStorage
          localStorage.setItem("currentUser", JSON.stringify(data));
          localStorage.setItem("token", data.token);
          localStorage.setItem("role", data.role);
          localStorage.setItem("uuid", data.uuid);
          localStorage.setItem("user_auth", JSON.stringify(user_auth));
          localStorage.setItem("ssoToken", data.token);
          localStorage.setItem("company_name", data?.company_name);
          localStorage.setItem(
            "company_id",
            data?.customers ? data?.customers[0] : "null"
          );

          // Important: Set all storage items before reload
            localStorage.setItem("hasRefreshed", "true");
            console.log("User authenticated and data stored:", data);
            // Call SubscriptionFlow API to create customer
            fetch('https://deviare.subscriptionflow.com/api/v1/customers', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************Saw0R4ohNEbfkqABfpQW6JUskJyD9bzOYMjVjyeRIvO-dODnK4fFiRyw6Rl9ILI6Kj_yBKQpYfCZRtV7LUiEOuuZ8U__8FtKuljUsDGAzum4os1MalFXV_cxOc3CxYucJd8phPeah4_LuNunroBzMzoPNCOkMASQg2cypYq-15wtv4k6QCdpOMqp5Zwddjw9oMLtme7ngBlOPoxk9jIWm1G78iSaUKHnXRf5kG4htEpnKXOmNp_KVrqwYVOF1b4Cei33bHbbUXyX_W28_buzZAuc73S0FB_S8E0yItP8w3LjIkoaQusfpZdJly4350sS82RNdPA_gC2o5itHa-X2hqJuZ_XTc25pKHu8N7qEzThstW8XEgnMm8MPrKTVFMlgmr_i-WJE4VCeySBV61pS145p0EFQnSnIDZj3jcv2ElfKwf1ieQh2H9ALDH8nLBv-Iy9zZZnYZiJ8YVpKIpCSFzunSukKc4_cwkcEUgzA8TFndV7sm_ROCtLJdFGlzKpjwUwiA5mc8AzuUFXh0DFpE5MGANHnPgOZDEMXQm8T3q8jCUyycSyprVaUDyNf8RQZYMe26821VcpZqm0DIf7gs`,
              },
              body: JSON.stringify({
                name:keycloak?.given_name ||"Sheeba",
                email: data?.email || "<EMAIL>",
                phone_number: "+****************",
                po_number: "PO12345",
                notes: "Important customer with special requirements.",
                image: "https://media.craiyon.com/2025-04-07/91CK_RsaQLeK1wOO4qn5sA.webp",
                balance: 1000,
                unapplied_amount: 0,
                billing_address_1: "123 Main St.",
                billing_address_2: "Suite 100",
                billing_city: "San Francisco",
                billing_state: "CA",
                billing_county: "San Francisco",
                billing_postal_code: "94105",
                billing_country: "US",
                shipping_address_1: "123 Main St.",
                shipping_address_2: "Suite 100",
                shipping_city: "San Francisco",
                shipping_state: "CA",
                shipping_county: "San Francisco",
                shipping_postal_code: "94105",
                shipping_country: "US",
                id: data?.uuid || "8ff541d7-1427-4205-bdbe-17741c6048b1"
              }),
            })
            .catch((err) => {
              console.error('Customer creation API error:', err);
            });

            // Use a small delay before reload to ensure storage is complete
            setTimeout(() => {
              window.location.reload();
            }, 100);
        } catch (error) {
          console.error("Authorization error:", error);
          dispatch({ type: 'LOGIN_ERROR', error: error.message });
        } finally {
          setIsLoading(false);
        }
      }
    } else {
      // Only redirect if we're not on the home page
      if (window.location.pathname !== '/') {
        window.location.href = '/';
      }
      setIsLoading(false);
    }
  };

  const handleOnEvent = (event, error) => {
    if (event === 'onAuthError' || event === 'onTokenExpired') {
      localStorage.clear();
      if (window.location.pathname !== '/') {
        window.location.href = '/';
      }
      return;
    }

    if (event === 'onAuthSuccess') {
      handleAuthentication(true);
    }

    if (event === 'onReady') {
      // Check for existing tokens
      const token = localStorage.getItem('SSO');
      const refreshToken = localStorage.getItem('refreshToken');

      if (token && refreshToken && !keycloak.authenticated) {
        console.log('Found existing tokens, attempting to restore session...');
        keycloak.init({
          ...initOptions,
          token,
          refreshToken,
          onLoad: 'check-sso'
        }).then(authenticated => {
          if (authenticated) {
            console.log('Session restored successfully');
          } else {
            console.log('Session restore failed, clearing tokens');
            localStorage.removeItem('SSO');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('ID:SSOToken');
          }
        });
      }
      setIsLoading(false);
    }

    if (error) {
      console.error('Keycloak error:', error);
      setIsLoading(false);
    }
  };

  // Force loading to false after 5 seconds
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  const handleTokens = (tokens) => {
    if (tokens) {
      console.log('Token refresh success');
    }
  };

  if (!keycloak) {
    return children;
  }
              

  return (
    <ReactKeycloakProvider
      authClient={keycloak}
      initOptions={{
        ...initOptions,
        checkLoginIframe: false // Disable iframe checking
      }}
      onEvent={handleOnEvent}
      onTokens={handleTokens}
      LoadingComponent={isLoading ? LoadingComponent : null}
    >
      {children}
    </ReactKeycloakProvider>
  );
};
