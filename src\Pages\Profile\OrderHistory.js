import React, { useEffect } from 'react';
import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';
import Layout from "../../Components/layouts";
import { CardContent, Container, IconButton, Typography } from '@material-ui/core';
import { useHistory } from 'react-router';
import variables from '../../Sys/variable.scss';
import ProfileSidebar from '../../Components/ProfileSidebar';
import Card from '@material-ui/core/Card';
import GenericTable from 'Components/GenericTable';
import { profileMenus } from './index';
import { CoursesServices } from 'Services/CoursesServices';
import userDefaultImage from 'Assets/images/user-default-image.png';
import PublicIcon from '@material-ui/icons/Public';
import { UserServices } from 'Services/UserServices';
import { logout, useAuthDispatch } from 'Context';
import { Paths } from 'Routes/routePaths';
import { Pagination } from '@material-ui/lab';
import moment from 'moment';
import { PDFDownloadLink } from '@react-pdf/renderer';
import OrderReceiptPDF from './OrderReceiptPDF';
import { GetApp } from '@material-ui/icons';

const useStyles = makeStyles((theme) => ({
    content: {
        minHeight: 305,
        maxWidth: 1040,
        marginTop: 20
    },
    inputStyle: {
        borderColor: '#fff',
        background: '#fff',
        borderRadius: '25px',
    },
    contentWrap: {
        width: '100%'
    },
    pageHeader: {
        display: 'flex',
        justifyContent: 'space-between',
        margin: '25px 0'
    },
    heading: {
        color: variables.labelcolor,
        fontSize: '23px',
        fontWeight: '500'
    },
    btn: {
        padding: '13px 35px',
        fontSize: '13px',
        color: 'white',
        borderRadius: '5px',
        marginLeft: '15px',
    },
    cancelBtn: {
        padding: '13px 35px',
        background: '#B7B7B7',
        fontSize: '13px',
        color: 'white',
        borderRadius: '5px',
        marginLeft: '15px',
        "&:hover": {
            background: "#454343"
        }
    },
    placeBtn: {
        display: 'flex',
        justifyContent: 'end',
        padding: '20px 0',
        marginTop: 20
    },
    imgIcon: {
        background: '#090909',
        borderRadius: '50%',
    },
    input: {
        display: 'none'
    },
    icon: {
        position: 'relative',
        margin: '1px auto 24px',
        borderRadius: '50%',
        maxWidth: 136,
        textAlign: 'center'
    },
    iconStyle: {
        borderRadius: '50%',
        color: '#000',
        background: '#fff',
        border: '1px solid #808080'
    },
    iconbtn: {
        bottom: '-17px',
        left: '51px',
        position: 'absolute'
    },
    sidebarContainer: {
        marginBottom: 20
    },
    profileContainer: {
        background: '#fff'
    },
    profileTitle: {
        fontSize: 14,
        paddingBottom: 10,
        '& .MuiSvgIcon-root': {
            position: 'relative',
            top: 6
        }
    }
}));

const coursesService = new CoursesServices();
const userServices = new UserServices();


const OrderHistory = () => {

    const classes = useStyles();
    // const isMobile = IsMobileDevice('1050');
    const dispatch = useAuthDispatch();
    const [orderHistory, setOrderHistory] = React.useState([]);
    const [loading, setLoading] = React.useState(false);
    const [userDetails, setUserDetails] = React.useState(null);
    const [filterState, setFilterState] = React.useState({ page: 1 });
    const [pagination, setPagination] = React.useState(null);
    // const [selectedCourse, setSelectedCourse] = React.useState(null);

    const history = useHistory();

    useEffect(() => {
        getUserProfile();
        //eslint-disable-next-line
    }, []);

    async function getUserProfile() {
        const user = await userServices.getDetailMyProfile();
        if (user && user.data && user.data.length) {
            const userData = user.data[0];
            setUserDetails(userData);
            getOrderTransections(filterState);
        }
    }

    async function getOrderTransections(queryData, loading = true) {
        setLoading(loading);
        let query = `page=${queryData.page}`;
        const transactions = await coursesService.getOrderTransections(query);
        const transactionList = transactions?.results.map(itm => {
            const invoiceId = itm?.transaction_id ? itm?.transaction_id.split("-")[0] : null;
            return {
                ...itm,
                transaction_date: itm.transaction_date ? moment(itm.transaction_date).format('LLL') : '-',
                courseName: itm?.course?.name || 'N/A',
                status: (<span style={{ color: '#03b603', fontWeight: 'bold' }}>Success</span>),
                invoiceDownload: (
                    <PDFDownloadLink document={<OrderReceiptPDF key={itm.uuid} invoiceDetails={{ ...itm, ...userDetails, invoiceId, courseName: itm?.course?.name || 'N/A' }} />} fileName={`order-receipt-${invoiceId}.pdf`}>
                        {({ loading }) => ((loading) ? '...' : <IconButton>
                            <GetApp color="primary" />
                        </IconButton>)}
                    </PDFDownloadLink>),

            };
        }) || [];
        setOrderHistory(transactionList);
        setPagination({ totalCount: transactions?.count || 0 });
        setLoading(false);
    }

    const handleItemClick = async (path) => {
        if (path === 'logout') {
            await logout(dispatch);
            window.location.href = Paths.MainPage;
        } else {
            history.push({ pathname: path });
        }
    }

    const handlePageChange = (evt, page) => {
        setFilterState({ ...filterState, page });
        return getOrderTransections({ ...filterState, page }, false);
    };

    const columns = [
        {
            label: "Order ID",
            key: "transaction_id"
        },
        {
            label: "Course Name",
            key: "courseName"
        },
        {
            label: "Date",
            key: "transaction_date"
        },
        {
            label: "Amount",
            key: "amount"
        },
        {
            label: "Status",
            key: "status"
        },
        {
            label: 'Download Receipt',
            key: 'invoiceDownload'
        }
    ];

    return (
        <Layout>
            <main>
                <Container className={classes.content}>
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={12} lg={3}>
                            <Card style={{ background: '#F7F9FA', marginBottom: 20 }}>
                                <CardContent style={{ textAlign: 'center' }}>
                                    <div className={classes.icon}>
                                        <img
                                            alt="logo"
                                            width='100%'
                                            className={classes.imgIcon}
                                            src={userDetails?.profile_image ? userDetails?.profile_image : userDefaultImage}
                                        />
                                    </div>
                                    <Typography style={{ fontSize: 20, fontWeight: 700, lineHeight: '24px', color: '#4D5766' }} variant="subtitle2" className={classes.profileTitle} >
                                        {userDetails?.firstName}
                                    </Typography>
                                    <Typography variant="subtitle2" className={classes.profileTitle} >
                                        @{userDetails?.lastName}
                                    </Typography>
                                    <Typography variant="subtitle2" className={classes.profileTitle} >
                                        <PublicIcon /> {userDetails?.country}
                                    </Typography>
                                </CardContent>
                            </Card>
                            <Card style={{ background: '#F7F9FA', marginBottom: 20 }}>
                                <div className={classes.sidebarContainer}>
                                    <ProfileSidebar menuItems={profileMenus} handleItemClick={handleItemClick} />
                                </div>
                            </Card>
                        </Grid>
                        <Grid item xs={12} sm={12} lg={9}>
                            <Card style={{ background: '#F7F9FA', marginBottom: 20, minHeight: 350 }}>
                                <CardContent>
                                    <Grid container>
                                        <Grid item xs={12}>
                                            <Typography
                                                variant="h4"
                                                style={{ paddingBottom: 20, fontWeight: 700 }}
                                                className={classes.heading}
                                                color="textPrimary">
                                                Order History
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={12}>
                                            <GenericTable columns={columns} loading={loading} list={orderHistory} />
                                        </Grid>
                                        <Grid item xs={12} style={{ display: 'flex', marginTop: 20 }}>
                                            {pagination &&
                                                <Pagination
                                                    onChange={handlePageChange}
                                                    style={{ margin: "0 auto" }}
                                                    count={parseInt(Math.ceil(pagination.totalCount / 10))}
                                                    variant="outlined"
                                                    shape="rounded"
                                                    siblingCount={0}
                                                />}
                                        </Grid>
                                        {/* {selectedCourse &&
                                        <Grid item xs={12} style={{marginTop: 20}}>
                                            <PDFViewer width={"100%"} height={"500px"}>
                                                <OrderReceiptPDF key={selectedCourse.uuid} invoiceDetails={selectedCourse} />
                                            </PDFViewer>
                                        </Grid>} */}
                                    </Grid>
                                </CardContent>
                            </Card>
                        </Grid>
                    </Grid>
                </Container>
            </main>
        </Layout>
    );
}

export default OrderHistory;