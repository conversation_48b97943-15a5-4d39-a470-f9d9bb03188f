{"name": "deviare-frontend", "version": "0.1.0", "private": true, "dependencies": {"@date-io/date-fns": "1.3.13", "@fullcalendar/daygrid": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@fullcalendar/react": "^5.11.2", "@fullcalendar/timegrid": "^5.11.3", "@material-ui/core": "^4.9.0", "@material-ui/data-grid": "^4.0.0-alpha.37", "@material-ui/icons": "^4.11.3", "@material-ui/lab": "^4.0.0-alpha.61", "@material-ui/pickers": "^3.3.10", "@mui/icons-material": "^7.3.2", "@react-google-maps/api": "^2.19.2", "@react-keycloak/web": "^3.4.0", "@react-pdf/renderer": "^2.3.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^0.27.2", "base-64": "^1.0.0", "classnames": "^2.3.1", "color": "^4.2.3", "crypto-js": "^4.1.1", "date-fns": "^2.29.3", "dateformat": "^5.0.3", "file-saver": "^2.0.5", "keycloak-js": "^26.1.2", "lodash": "^4.17.21", "material-ui-color": "^1.2.0", "materialui-daterange-picker": "^1.1.92", "moment": "^2.29.4", "mui-datatables": "^3.7.7", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-lazy-load-image-component": "^1.6.0", "react-leaf-carousel": "^3.0.0", "react-mui-stepper": "0.1.7", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.3.4", "react-player": "^2.12.0", "react-router": "^5.1.2", "react-router-dom": "^5.1.2", "react-scripts": "5.0.1", "react-svg-gauge": "^1.0.10", "react-swipeable-views": "^0.14.0", "react-to-print": "^2.14.10", "react-toastify": "^9.0.8", "recharts": "^2.1.13", "seamless-immutable": "^7.1.4", "swiper": "^9.1.1", "uuid": "^9.0.0", "uuidv4": "^6.2.13", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "proxy": "https://api-staging.deviare.africa", "scripts": {"start": "cross-env GENERATE_SOURCEMAP=false react-app-rewired start", "build": "cross-env GENERATE_SOURCEMAP=false react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"assert": "^2.0.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "cross-env": "^7.0.3", "process": "^0.11.10", "react-app-rewired": "^2.2.1", "sass": "^1.84.0", "stream-browserify": "^3.0.0", "util": "^0.12.5"}, "resolutions": {"@react-keycloak/core": "3.2.0", "@react-keycloak/web": "3.4.0", "readdirp": "3.6.0", "expect": "^27.5.1"}}