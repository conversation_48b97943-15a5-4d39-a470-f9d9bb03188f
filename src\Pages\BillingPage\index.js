import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import { useHistory } from 'react-router-dom';
import TextField from '@material-ui/core/TextField';
import IconButton from '@material-ui/core/IconButton';
import CheckIcon from '@material-ui/icons/Check';
import CloseIcon from '@material-ui/icons/Close';
import EditIcon from '@material-ui/icons/Edit';
import CircularProgress from '@material-ui/core/CircularProgress';
import Snackbar from '@material-ui/core/Snackbar';
import Alert from '@material-ui/lab/Alert';
import Button from '@material-ui/core/Button';

const useStyles = makeStyles((theme) => ({
  root: {
    minHeight: '100vh',
    background: '#f8f9fa',
    padding: theme.spacing(3),
  },
  container: {
    maxWidth: 1200,
    margin: '0 auto',
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(4),
  },
  title: {
    fontSize: '2rem',
    fontWeight: 600,
    color: '#212529',
    margin: 0,
  },
  headerButtons: {
    display: 'flex',
    gap: theme.spacing(1),
  },
  cancelButton: {
    backgroundColor: 'transparent',
    color: '#6c757d',
    border: '1px solid #dee2e6',
    borderRadius: 8,
    padding: '10px 20px',
    fontWeight: 500,
    textTransform: 'none',
    '&:hover': {
      backgroundColor: '#f8f9fa',
      borderColor: '#adb5bd',
    },
  },
  upgradeButton: {
    border: 'none',
    borderRadius: 8,
    padding: '10px 20px',
    fontWeight: 500,
    textTransform: 'none',
    '&:hover': {
      backgroundColor: '#0056b3',
    },
  },
  mainContent: {
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    gap: theme.spacing(3),
    marginBottom: theme.spacing(4),
    alignItems: 'stretch', // This ensures both cards stretch to the same height
    [theme.breakpoints.down('md')]: {
      gridTemplateColumns: '1fr',
    },
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: theme.spacing(3),
    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
    border: '1px solid #e9ecef',
    display: 'flex',
    flexDirection: 'column',
    height: '100%', // This ensures the card takes full height of the grid cell
  },
  cardTitle: {
    fontSize: '1.25rem',
    fontWeight: 600,
    color: '#495057',
    marginBottom: theme.spacing(2),
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  editButton: {
    color: '#007bff',
    fontSize: '0.875rem',
    fontWeight: 500,
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'pointer',
    padding: '4px 8px',
    borderRadius: 4,
    '&:hover': {
      backgroundColor: '#f8f9fa',
    },
  },
  planCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: theme.spacing(3),
    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
    border: '1px solid #e9ecef',
    display: 'flex',
    flexDirection: 'column',
    height: '100%', // This ensures the card takes full height of the grid cell
  },
  planName: {
    fontSize: '1.5rem',
    fontWeight: 600,
    color: '#212529',
    marginBottom: theme.spacing(1),
  },
  invoicePeriod: {
    color: '#6c757d',
    fontSize: '0.875rem',
    marginBottom: theme.spacing(1),
    fontWeight: 600,
  },
  invoicePeriodValue: {
    color: '#6c757d',
    fontSize: '0.875rem',
    marginBottom: theme.spacing(2),
  },
  billingSection: {
    display: 'grid',
    gridTemplateColumns: '1fr 2fr',
    gap: theme.spacing(4),
    flex: 1, // This makes the billing section fill the remaining space
    alignItems: 'start', // Aligns content to the top
    [theme.breakpoints.down('sm')]: {
      gridTemplateColumns: '1fr',
    },
  },
  planContent: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    justifyContent: 'flex-start', // Aligns content to the top
  },
  planDetails: {
    marginTop: theme.spacing(2),
    flex: 1, // This will push content to fill available space if needed
  },
  fieldGroup: {
    marginBottom: theme.spacing(2),
  },
  fieldLabel: {
    fontSize: '0.875rem',
    fontWeight: 600,
    color: '#495057',
    marginBottom: theme.spacing(0.5),
  },
  fieldValue: {
    fontSize: '0.875rem',
    color: '#212529',
    marginBottom: theme.spacing(1),
    lineHeight: 1.5,
  },
  editField: {
    marginBottom: theme.spacing(1),
    '& .MuiOutlinedInput-root': {
      fontSize: '0.875rem',
      '& fieldset': {
        borderColor: '#dee2e6',
      },
      '&:hover fieldset': {
        borderColor: '#adb5bd',
      },
    },
  },
  historySection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: theme.spacing(3),
    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
    border: '1px solid #e9ecef',
  },
  historyTitle: {
    fontSize: '1.25rem',
    fontWeight: 600,
    color: '#495057',
    marginBottom: theme.spacing(3),
  },
  table: {
    width: '100%',
    borderCollapse: 'collapse',
  },
  tableHeader: {
    borderBottom: '1px solid #dee2e6',
  },
  th: {
    textAlign: 'left',
    fontWeight: 600,
    fontSize: '0.875rem',
    color: '#495057',
    padding: theme.spacing(1.5, 1),
    '&:first-child': {
      paddingLeft: 0,
    },
    '&:last-child': {
      paddingRight: 0,
    },
  },
  tableRow: {
    borderBottom: '1px solid #f8f9fa',
    '&:last-child': {
      borderBottom: 'none',
    },
  },
  td: {
    padding: theme.spacing(1.5, 1),
    fontSize: '0.875rem',
    color: '#212529',
    '&:first-child': {
      paddingLeft: 0,
    },
    '&:last-child': {
      paddingRight: 0,
      textAlign: 'right',
    },
  },
  moreButton: {
    backgroundColor: 'transparent',
    border: 'none',
    fontSize: '1.2rem',
    color: '#6c757d',
    cursor: 'pointer',
    padding: 4,
    borderRadius: 4,
    '&:hover': {
      backgroundColor: '#f8f9fa',
    },
  },
  pagination: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    gap: theme.spacing(1),
    marginTop: theme.spacing(3),
  },
  pageButton: {
    minWidth: 32,
    height: 32,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    border: 'none',
    borderRadius: 6,
    fontSize: '0.875rem',
    color: '#6c757d',
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: '#f8f9fa',
    },
    '&.active': {
      backgroundColor: '#007bff',
      color: '#fff',
    },
  },
  loadingContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 400,
    gap: theme.spacing(2),
  },
  errorContainer: {
    textAlign: 'center',
    padding: theme.spacing(4),
  },
  errorTitle: {
    fontSize: '1.5rem',
    fontWeight: 600,
    color: '#dc3545',
    marginBottom: theme.spacing(2),
  },
  errorMessage: {
    color: '#6c757d',
    marginBottom: theme.spacing(3),
  },
  retryButton: {
    backgroundColor: '#007bff',
    color: '#fff',
    border: 'none',
    borderRadius: 8,
    padding: '10px 20px',
    fontSize: '0.875rem',
    fontWeight: 500,
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: '#0056b3',
    },
  },
  saveButton: {
    color: '#28a745',
    padding: 4,
    '&:hover': {
      backgroundColor: 'rgba(40, 167, 69, 0.1)',
    },
  },
  cancelEditButton: {
    color: '#dc3545',
    padding: 4,
    '&:hover': {
      backgroundColor: 'rgba(220, 53, 69, 0.1)',
    },
  },
}));

function BillingPage() {
  const classes = useStyles();
  const history = useHistory();
  
  // State for user ID and API data
  const [userId, setUserId] = useState(null);
  const [customerData, setCustomerData] = useState(null);
  const [invoiceData, setInvoiceData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const pageSize = 10;

  // Edit states
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    name: '',
    email: '',
    phone_number: '',
    billing_address_1: '',
    billing_address_2: '',
    billing_city: '',
    billing_state: '',
    billing_postal_code: '',
    billing_country: '',
  });
  const [loading, setLoading] = useState(false);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Function to safely get UUID from localStorage
  const getUserId = () => {
    try {
      const id = localStorage.getItem('uuid');
      if (!id) {
        console.error('No UUID found in localStorage');
        setHasError(true);
        setErrorMessage('User ID not found. Please log in again.');
        return null;
      }
      console.log('Retrieved UUID from localStorage:', id);
      return id;
    } catch (error) {
      console.error('Error reading UUID from localStorage:', error);
      setHasError(true);
      setErrorMessage('Unable to retrieve user information. Please refresh the page.');
      return null;
    }
  };

  // Initialize userId on component mount
  useEffect(() => {
    const id = getUserId();
    if (id) {
      setUserId(id);
    } else {
      setFetchLoading(false);
    }
  }, []);

  // Fetch billing data when userId is available
  useEffect(() => {
    if (userId) {
      fetchBillingData();
    }
  }, [userId, currentPage]);

  const fetchBillingData = () => {
    if (!userId) {
      console.error('No user ID available for API call');
      setHasError(true);
      setErrorMessage('User ID is required to load billing information.');
      setFetchLoading(false);
      return;
    }

    setFetchLoading(true);
    setHasError(false);

    console.log(`Fetching billing data for user ID: ${userId}`);

    fetch(`https://api-staging.deviare.africa/main/Billing/${userId}/`)
      .then((res) => {
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        return res.json();
      })
      .then((response) => {
        console.log('API Response:', response);
        
        if (response.success && response.data) {
          if (response.data.customer) {
            setCustomerData(response.data.customer);
            
            setEditData({
              name: response.data.customer.name || '',
              email: response.data.customer.email || '',
              phone_number: response.data.customer.phone_number || '',
              billing_address_1: response.data.customer.billing_address_1 || '',
              billing_address_2: response.data.customer.billing_address_2 || '',
              billing_city: response.data.customer.billing_city || '',
              billing_state: response.data.customer.billing_state || '',
              billing_postal_code: response.data.customer.billing_postal_code || '',
              billing_country: response.data.customer.billing_country || '',
            });
          }
          
          if (response.data.invoices) {
            setInvoiceData(response.data.invoices);
            const totalInvoices = response.data.invoices.length;
            setTotalPages(Math.ceil(totalInvoices / pageSize) || 1);
          }
        } else {
          setHasError(true);
          setErrorMessage(response.message || 'No billing data found for this user.');
        }
        setFetchLoading(false);
      })
      .catch((err) => {
        console.error('Error fetching billing data:', err);
        setHasError(true);
        setErrorMessage('Failed to load billing information. Please try again.');
        setFetchLoading(false);
      });
  };

  const handleEditSave = async () => {
    if (!editData.name.trim()) {
      setNotification({
        open: true,
        message: 'Name is required',
        severity: 'error'
      });
      return;
    }

    if (!userId) {
      setNotification({
        open: true,
        message: 'User ID not found. Please log in again.',
        severity: 'error'
      });
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(
        `https://api-staging.deviare.africa/main/Billing/${userId}/update`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(editData),
        }
      );

      if (response.ok) {
        setCustomerData(prev => ({
          ...prev,
          ...editData
        }));

        setIsEditing(false);
        setNotification({
          open: true,
          message: 'Customer details updated successfully',
          severity: 'success'
        });
      } else {
        throw new Error('Failed to update customer details');
      }
    } catch (error) {
      console.error('Update error:', error);
      setNotification({
        open: true,
        message: 'Failed to update customer details. Please try again.',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = () => {
    const id = getUserId();
    if (id) {
      setUserId(id);
      setHasError(false);
      setErrorMessage('');
    }
  };

  const getCurrentPlan = () => {
    if (!invoiceData.length) return null;
    
    const latestInvoice = invoiceData.reduce((latest, invoice) => {
      const latestDate = new Date(latest.invoice_date);
      const currentDate = new Date(invoice.invoice_date);
      return currentDate > latestDate ? invoice : latest;
    });

    return latestInvoice;
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      return dateString;
    }
  };

  const formatCurrency = (amount, currency = 'ZAR') => {
    if (amount === null || amount === undefined) return 'N/A';
    
    try {
      if (currency === 'ZAR') {
        return `R${parseFloat(amount).toFixed(2)}`;
      }
      return `${currency} ${parseFloat(amount).toFixed(2)}`;
    } catch (error) {
      return `${currency} ${amount}`;
    }
  };

  const handleEditStart = () => {
    setIsEditing(true);
  };

  const handleEditCancel = () => {
    setIsEditing(false);
    if (customerData) {
      setEditData({
        name: customerData.name || '',
        email: customerData.email || '',
        phone_number: customerData.phone_number || '',
        billing_address_1: customerData.billing_address_1 || '',
        billing_address_2: customerData.billing_address_2 || '',
        billing_city: customerData.billing_city || '',
        billing_state: customerData.billing_state || '',
        billing_postal_code: customerData.billing_postal_code || '',
        billing_country: customerData.billing_country || '',
      });
    }
  };

  const handleInputChange = (field, value) => {
    setEditData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handleCancelSubscription = () => {
    history.push('/subscribe/cancel');
  };

  const handleUpgradePlan = () => {
    // Add upgrade logic here
    console.log('Upgrade plan clicked');
  };

  // Show loading state
  if (fetchLoading) {
    return (
      <div className={classes.root}>
        <div className={classes.loadingContainer}>
          <CircularProgress size={50} />
          <div style={{ fontSize: '1.1rem', color: '#6c757d' }}>
            Loading billing information...
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (hasError) {
    return (
      <div className={classes.root}>
        <div className={classes.errorContainer}>
          <div className={classes.errorTitle}>
            Unable to Load Billing Information
          </div>
          <div className={classes.errorMessage}>
            {errorMessage}
          </div>
          <button 
            className={classes.retryButton}
            onClick={handleRetry}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  const currentPlan = getCurrentPlan();

  // Get current page invoices
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentInvoices = invoiceData.slice(startIndex, endIndex);

  return (
    <div className={classes.root}>
      <div className={classes.container}>
        {/* Header */}
        <div className={classes.header}>
          <h1 className={classes.title}>Plan and Billing</h1>
          <div className={classes.headerButtons}>
            <Button 
              className={classes.cancelButton}
              onClick={handleCancelSubscription}
            >
              Cancel Subscription
            </Button>
            <Button 
              color="primary"
              className={classes.upgradeButton}
              onClick={handleUpgradePlan}
            >
              Upgrade Plan
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <div className={classes.mainContent}>
          {/* Current Plan */}
          <div className={classes.planCard}>
            <div className={classes.planContent}>
              <div className={classes.planName}>
                {currentPlan?.plan || 'Freemium'}
              </div>
              <div className={classes.planDetails}>
                <div className={classes.invoicePeriod}>
                  Invoice Period
                </div>
                <div className={classes.invoicePeriodValue}>
                  12 months
                </div>
              </div>
            </div>
          </div>

          {/* Billing Details */}
          <div className={classes.card}>
            <div className={classes.cardTitle}>
              Billing Details
              <button 
                className={classes.editButton}
                onClick={isEditing ? undefined : handleEditStart}
                disabled={loading}
              >
                {isEditing ? (
                  <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
                    {loading && <CircularProgress size={16} />}
                    <IconButton
                      className={classes.saveButton}
                      onClick={handleEditSave}
                      disabled={loading}
                      size="small"
                    >
                      <CheckIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      className={classes.cancelEditButton}
                      onClick={handleEditCancel}
                      disabled={loading}
                      size="small"
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </div>
                ) : (
                  'Edit'
                )}
              </button>
            </div>
            
            <div className={classes.billingSection}>
              {/* Name */}
              <div>
                <div className={classes.fieldLabel}>Name</div>
                {!isEditing ? (
                  <div className={classes.fieldValue}>
                    {customerData?.name || 'composer'}
                  </div>
                ) : (
                  <TextField
                    fullWidth
                    variant="outlined"
                    size="small"
                    value={editData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={classes.editField}
                    disabled={loading}
                  />
                )}
              </div>

              {/* Billing Address */}
              <div>
                <div className={classes.fieldLabel}>Billing Address</div>
                {!isEditing ? (
                  <div className={classes.fieldValue}>
                    {customerData?.billing_address_1 || '123 Main St.'}<br />
                    {customerData?.billing_city || 'San Francisco'}, {customerData?.billing_state || 'CA'},<br />
                    {customerData?.billing_postal_code || '94105'}
                  </div>
                ) : (
                  <div>
                    <TextField
                      fullWidth
                      placeholder="Address Line 1"
                      variant="outlined"
                      size="small"
                      value={editData.billing_address_1}
                      onChange={(e) => handleInputChange('billing_address_1', e.target.value)}
                      className={classes.editField}
                      disabled={loading}
                    />
                    <TextField
                      fullWidth
                      placeholder="Address Line 2"
                      variant="outlined"
                      size="small"
                      value={editData.billing_address_2}
                      onChange={(e) => handleInputChange('billing_address_2', e.target.value)}
                      className={classes.editField}
                      disabled={loading}
                    />
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 8 }}>
                      <TextField
                        placeholder="City"
                        variant="outlined"
                        size="small"
                        value={editData.billing_city}
                        onChange={(e) => handleInputChange('billing_city', e.target.value)}
                        className={classes.editField}
                        disabled={loading}
                      />
                      <TextField
                        placeholder="Postal Code"
                        variant="outlined"
                        size="small"
                        value={editData.billing_postal_code}
                        onChange={(e) => handleInputChange('billing_postal_code', e.target.value)}
                        className={classes.editField}
                        disabled={loading}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Billing History */}
        <div className={classes.historySection}>
          <div className={classes.historyTitle}>Billing History</div>
          
          <table className={classes.table}>
            <thead className={classes.tableHeader}>
              <tr>
                <th className={classes.th}>Invoice Date</th>
                <th className={classes.th}>Invoice ID</th>
                <th className={classes.th}>Amount</th>
                <th className={classes.th}></th>
              </tr>
            </thead>
            <tbody>
              {currentInvoices.length > 0 ? (
                currentInvoices.map((invoice, idx) => (
                  <tr key={invoice.name || idx} className={classes.tableRow}>
                    <td className={classes.td}>
                      {formatDate(invoice.invoice_date)}
                    </td>
                    <td className={classes.td}>
                      {invoice.name || 'INV-0001'}
                    </td>
                    <td className={classes.td}>
                      {formatCurrency(invoice.total_amount, invoice.currency)}
                    </td>
                    <td className={classes.td}>
                      <button className={classes.moreButton}>⋯</button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr className={classes.tableRow}>
                  <td colSpan={4} className={classes.td} style={{ textAlign: 'center', color: '#6c757d' }}>
                    No invoices found
                  </td>
                </tr>
              )}
            </tbody>
          </table>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className={classes.pagination}>
              <button
                className={classes.pageButton}
                onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                disabled={currentPage === 1}
              >
                ‹
              </button>
              {[...Array(Math.min(5, totalPages))].map((_, i) => {
                const page = i + 1;
                return (
                  <button
                    key={page}
                    className={`${classes.pageButton} ${currentPage === page ? 'active' : ''}`}
                    onClick={() => handlePageChange(page)}
                  >
                    {page}
                  </button>
                );
              })}
              <button
                className={classes.pageButton}
                onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                ›
              </button>
            </div>
          )}
        </div>
      </div>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity}>
          {notification.message}
        </Alert>
      </Snackbar>
    </div>
  );
}

export default BillingPage;
