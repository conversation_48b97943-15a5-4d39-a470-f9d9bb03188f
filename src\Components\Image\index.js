import React, { useState, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import { makeStyles, withTheme, Box } from '@material-ui/core';

const styles = {
  imageContainer: {
    width: '100%',
    paddingTop: '100%',
    overflow: 'hidden',
    position: 'relative',
    maxWidth: props => props.width || '100%'
  },
  image: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    margin: 'auto'
  }
};
const useStyles = makeStyles(styles);

const Image = withTheme(({ src, roundedBorder, theme, ...props }) => {
  const classes = useStyles({ ...props });
  const img = useRef();
  const [hasMoreWidthThanHeight, setHasMoreWidthThanHeight] = useState(null);
  const [hasLoaded, setHasLoaded] = useState(false);

  const onLoad = useCallback(() => {
    if (img.current.naturalHeight < img.current.naturalWidth) {
      setHasMoreWidthThanHeight(true);
    } else {
      setHasMoreWidthThanHeight(false);
    }
    setHasLoaded(true);
  }, [img, setHasLoaded, setHasMoreWidthThanHeight]);

  return (
    <Box className={classes.imageContainer}>
      <img
        style={{
          height: hasMoreWidthThanHeight ? props.width || '100%' : 'auto',
          width: hasMoreWidthThanHeight ? 'auto' : props.width || '100%',
          display: hasLoaded ? 'block' : 'none',
          borderRadius: roundedBorder ? theme.shape.borderRadius : 0
        }}
        ref={img}
        className={classes.image}
        onLoad={onLoad}
        src={src}
        alt=""
      />
    </Box>
  );
});

Image.propTypes = {
  src: PropTypes.string.isRequired,
  title: PropTypes.string,
  roundedBorder: PropTypes.bool,
  options: PropTypes.arrayOf(PropTypes.object)
};

export default Image;
