import React from 'react';
import {Text, View, StyleSheet } from '@react-pdf/renderer';

const styles = StyleSheet.create({
    mainTable: {
        marginBottom: 20,
        borderBottomColor: '#9696A4',
        borderBottomWidth: 1,
        borderStyle: 'dashed',
        fontSize: 12
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        height: 24,
        fontSize: 12,
        fontStyle: 'bold'
    },
    description: {
        width: '25%'
    },
    head1: {
        width: '25%',
        textAlign: "center"
    },
    qty: {
        width: '10%',
        textAlign: "center"
    },
    tax: {
        width: '20%',
        textAlign: "center"
    },
    rate: {
        width: '20%',
        paddingRight: 10,
        textAlign: "right"
    }
});

const InvoiceTableRow = ({ items }) => {
    return (<View style={styles.mainTable}>
        {
            items.map( (item, key) => {
                return (<View key={key} style={styles.row}>
                    <Text style={styles.description}>{item.date}</Text>
                    <Text style={styles.head1}>{item.course}</Text>
                    <Text style={styles.tax}>Standard</Text>
                    <Text style={styles.qty}>1</Text>
                    <Text style={styles.rate}>{item.amount}</Text>
                </View>)
            })
        }
    </View>);
};
  
  export default InvoiceTableRow