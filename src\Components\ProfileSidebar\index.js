import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import { ListItemIcon, MenuItem, MenuList } from '@material-ui/core';
import { useLocation } from 'react-router-dom';

const useStyles = makeStyles((theme) => ({
    menuItemText: {
        color: "#fff",
        '&.MuiTypography-body1':{
            color: "#fff"
        }
    },
    sidebarMenu: {
        paddingTop: 10,
        '& ..MuiListItemIcon-root': {
            minWidth: 36,
            fontSize: 16
        },
        "& .filterMenuItem": {
            height: 40,
            fontSize: 16,
            '&.Mui-selected': {
                height: 40,
                background: 'white !important',
                color: '#0080CA !important',
                '& .MuiListItemIcon-root': {
                    color: '#0080CA !important'
                }
            },
            "&:hover": {
                backgroundColor: theme.palette.common.white,
                color: theme.palette.primary.main
            }
        }
    }
}));

const ProfileSidebar = ({ menuItems, handleItemClick }) => {
    const classes = useStyles();
    const location = useLocation();

    return (
        <>
            <MenuList className={classes.sidebarMenu}>
                {menuItems.map((item, index) => (
                    <MenuItem className='filterMenuItem' 
                    onClick={() => handleItemClick(item.active)} 
                    selected={location.pathname.startsWith(item.active)} 
                    key={index} >
                        <ListItemIcon>
                            {item.icon}
                        </ListItemIcon>
                        {item.title}
                    </MenuItem>
                ))}
            </MenuList>
        </>
    );
}

export default ProfileSidebar;