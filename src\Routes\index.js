import React from 'react';
import { Switch, BrowserRouter as Router } from 'react-router-dom';
import { Paths } from './routePaths';
import PageNotFound from '../Pages/PageNotFound';
import { AuthProvider } from '../Context';
import AppRoutes from './AppRoutes';
import Profile from '../Pages/Profile';
import ChangePassword from '../Pages/Profile/ChangePassword';
import StartLearning from 'Pages/StartLearningTab/index';
import ForgotPassword from 'Pages/ForgotPassword';
import MainPage from 'Pages/MainPage';
import HomePage from 'Pages/Home';
import CourseListing from 'Pages/CourseListing';
import CourseLearning from 'Pages/CourseLearning/index';
import CourseDetails from 'Pages/CourseListing/CourseDetails';
import Checkout from 'Pages/CourseListing/Checkout';
import TermsAndCondition from 'Pages/TermsAndCondition';
import PrivacyPolicy from 'Pages/PrivacyPolicy';
import OrderHistory from 'Pages/Profile/OrderHistory';
import ScrollOnTop from './ScrollOnTop';
import Accreditations from 'Pages/Accreditations';
import Certificates from 'Pages/Profile/Certificates';
import RegisteredCourses from 'Pages/RegisteredCourses';
import BillingPage from 'Pages/BillingPage';
import StartLearningProfile from 'Pages/StartLearningProfile';
import StartLearningChangePassword from 'Pages/StartLearningChangePassword';
import CancelSubscription from 'Pages/CancelSubscription';
import OrderConfirmation from 'Pages/OrderConfirmation';
import AccessCourseDetail from 'Pages/AccessCourseDetail';
const Routes = () => {

    const routes = [
        {
            path: '/',
            component: MainPage,
            isPrivate: false,
            exact: true
        },
        {
            path: Paths.ForgotPassword,
            component: ForgotPassword,
            isPrivate: false
        },
        {
            path: Paths.ChangePassword,
            component: ChangePassword,
            isPrivate: true
        },
        {
            path: Paths.Profile,
            component: Profile,
            isPrivate: false
        },
        {
            path: Paths.MainPage,
            component: MainPage,
            isPrivate: false
        },
        {
            path: Paths.HomePage,
            component: HomePage,
            isPrivate: true
        },
        {
            path: Paths.CourseListing,
            component: CourseListing,
            // isPrivate: true
        },
        {
            path:Paths.BillingPage,
            component:BillingPage,
            isPrivate:true
        },
        {
            path: Paths.CourseLearning,
            component: CourseLearning,
            isPrivate: true
        },
        {
          path:Paths.StartLearning,
            component:StartLearning,
            isPrivate:true
        },
        {
            path:Paths.StartLearningProfile,
            component:StartLearningProfile,
            isPrivate:true
        },
        {
            path:Paths.StartLearningChangePassword,
            component:StartLearningChangePassword,
            isPrivate:true
        },
        {
            path:Paths.CancelSubscription,
            component:CancelSubscription,
            isPrivate:true
        },
        {
            path:Paths.OrderConfirmation,
            component:OrderConfirmation,
            isPrivate:true
        },
        {
            path:Paths.AccessCourseDetail,
            component:AccessCourseDetail,
            isPrivate:true
        },
        {
            path: Paths.CourseDetails,
            component: CourseDetails,
            // isPrivate: true
        },
        {
            path: Paths.CheckoutCourse,
            component: Checkout,
            isPrivate: true
        },
        {
            path: Paths.TermsAndCondition,
            component: TermsAndCondition,
            isPrivate: false
        },
        {
            path: Paths.PrivacyPolicy,
            component: PrivacyPolicy,
            isPrivate: false
        },
        {
            path: Paths.OrderHistory,
            component: OrderHistory,
            isPrivate: false
        },
        {
            path: Paths.Accreditations,
            component: Accreditations,
            isPrivate: false
        },
        {
            path: Paths.Certificates,
            component: Certificates,
            isPrivate: false
        },
        {
            path: Paths.RegisteredCourses,
            component: RegisteredCourses,
            isPrivate: false
        },
        {
            path: '/*',
            component: PageNotFound,
            isPrivate: false
        }
    ];

    return (
        <AuthProvider>
            <Router>
                <ScrollOnTop />
                <Switch>
                    {routes.map((route) => (
                        <AppRoutes
                            exact={route.exact}
                            key={route.path}
                            path={route.path}
                            component={route.component}
                            isPrivate={route.isPrivate}
                        />
                    ))}
                </Switch>
            </Router>
        </AuthProvider>
    )
};

export default Routes;
