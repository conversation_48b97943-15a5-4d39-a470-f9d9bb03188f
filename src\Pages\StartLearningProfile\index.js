import React, { useState, useEffect } from 'react';
import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';
import Layout from "../../Components/layouts";
import { notify } from '../../utils';
import Input from '@material-ui/core/Input';
import { REQUIRED_ERROR } from '../../utils/constants';
import Typography from '@material-ui/core/Typography';
import Button from '@material-ui/core/Button';
import { UserServices } from '../../Services/UserServices';
import { CustomerServices } from '../../Services/CustomerServices';
import userDefaultImage from '../../Assets/images/user-default-image.png';
import IconButton from '@material-ui/core/IconButton';
import { CircularProgress, Container } from '@material-ui/core';
import { useHistory } from 'react-router';
import ProfileSidebar from '../../Components/ProfileSidebar';
import LibraryBooksIcon from '@material-ui/icons/LibraryBooks';
import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';
import PublicIcon from '@material-ui/icons/Public';
import EditIcon from '@material-ui/icons/Edit';
import EditProfileModal from '../Profile/EditProfileModal';
import AccountCircleIcon from '@material-ui/icons/AccountCircle';
import CardMembershipIcon from '@material-ui/icons/CardMembership';
import LockOpenIcon from '@material-ui/icons/LockOpen';
import ExitToAppIcon from '@material-ui/icons/ExitToApp';
import { Paths } from 'Routes/routePaths';
import { logout, useAuthDispatch } from 'Context';
import EditSkillsModal from '../Profile/EditSkillsModal';
import PhoneInputField from 'Components/PhoneInputField';
import { keycloak, initOptions } from '../../config/keycloak';
import { authorizeWithSSO, authService } from '../../Services/AuthService';
const userServices = new UserServices();
const customerServices = new CustomerServices();

const useStyles = makeStyles((theme) => ({
    content: {
        minHeight: 305,
        maxWidth: 1040,
        marginTop: 20
    },
    inputStyle: {
        borderColor: '#fff',
        background: '#fff',
        borderRadius: '25px',
    },
    pageHeader: {
        display: 'flex',
        justifyContent: 'space-between',
        margin: '25px 0'
    },
    heading: {
        color: '#4D5766',
        fontSize: 20,
        fontWeight: 700
    },
    btn: {
        padding: '13px 35px',
        fontSize: '13px',
        color: 'white',
        borderRadius: '5px',
        marginLeft: '15px',
    },
    cancelBtn: {
        padding: '13px 35px',
        background: '#B7B7B7',
        fontSize: '13px',
        color: 'white',
        borderRadius: '5px',
        marginLeft: '15px',
        "&:hover": {
            background: "#454343"
        }
    },
    placeBtn: {
        display: 'flex',
        justifyContent: 'end',
        padding: '20px 0',
        marginTop: 20
    },
    imgIcon: {
        background: '#090909',
        borderRadius: '50%',
    },
    input: {
        display: 'none'
    },
    icon: {
        position: 'relative',
        margin: '0px auto 20px',
        // background: '#090909',
        borderRadius: '50%',
        maxWidth: 136,
        textAlign: 'center'
    },
    iconStyle: {
        borderRadius: '50%',
        color: '#000',
        background: '#fff',
        border: '1px solid #808080'
    },
    iconbtn: {
        bottom: '-17px',
        left: '51px',
        position: 'absolute'
    },
    sidebarContainer: {
        marginBottom: 20
    },
    profileContainer: {
        background: '#fff'
    },
    profileTitle: {
        fontSize: 14,
        paddingBottom: 10,
        '& .MuiSvgIcon-root': {
            position: 'relative',
            top: 6
        }
    },
    phoneInputStyle: {
        marginBottom: 15,
        marginTop: 8,
        position: 'relative',
        '& .PhoneInputInput': {
            border: '1px solid #bebaba',
            borderRadius: '25px',
            padding: 13,
            paddingLeft: 48
        },
        '& .PhoneInputCountry': {
            position: 'absolute',
            left: 12,
            top: 14
        }
    }
}));

const initFormState = {
    firstName: '',
    lastName: '',
    country: '',
    email: '',
    userName: '',
    memberSince: '',
    profile_image: '',
    contact_no: '',
    city: '',
    pincode: '',
    date_of_birth: '',
    province: '',
    address: '',
    occupation: '',
    industry: '',
    language: '',
    marketingPreferences: '',
    referralSource: '',
};

export const profileMenus = [
    {
        title: 'Personal details',
        active: Paths.StartLearningProfile,
        icon: <AccountCircleIcon />
    },
    {
        title: 'My Orders',
        icon: <LibraryBooksIcon />,
        active: Paths.OrderHistory,
    },
    {
        title: 'Certificates',
        icon: <CardMembershipIcon />,
        active: Paths.Certificates,
    },
    // {
    //     title: 'Payment details',
    //     icon: <AccountBalanceWalletIcon />,
    //     active: Paths.OrderHistory,
    // },
    {
        title: 'Change Password',
        icon: <LockOpenIcon />,
        active: Paths.ChangePassword,
    },
    {
        title: 'Log Out',
        icon: <ExitToAppIcon />,
        active: 'logout',
    }
];

const StartLearningProfile = () => {
     const dispatch = useAuthDispatch();
    useEffect(() => {
        (async () => {
             localStorage.setItem("SSO", keycloak?.token);
                localStorage.setItem("ID:SSOToken", keycloak?.idToken);
                localStorage.setItem("session_state", keycloak?.tokenParsed?.session_state);
                localStorage.setItem("isLogin", true);
                localStorage.setItem("user", keycloak?.tokenParsed?.preferred_username);
                localStorage.setItem("refreshToken", keycloak?.refreshToken);
                localStorage.setItem("firstname", keycloak?.idTokenParsed.given_name);
                localStorage.setItem("lastname", keycloak?.family_name);
                localStorage.setItem("userid", keycloak?.sub);
            if (!localStorage.getItem("ssoToken")) {
                // Then call the API
                await authService.signUpUser({
                    firstName: keycloak?.idTokenParsed.given_name,
                    lastName: keycloak?.idTokenParsed.family_name,
                    email: keycloak?.tokenParsed?.preferred_username,
                    userName: keycloak?.tokenParsed?.preferred_username,
                    password: "123",
                customers: [
        "e6284686-4509-4b79-ac8d-732d7ca25a69"
    ],
    project_id: "6cbe6076-7c14-4bf9-ac68-511cd82cd5ea"
                });
              const res=  await authService.authorizeafterlogin(keycloak?.token);
              console.log("Response from authorizeafterlogin:", res);
               localStorage.setItem("SSO", keycloak?.token);
                localStorage.setItem("ID:SSOToken", keycloak?.idToken);
                localStorage.setItem("session_state", keycloak?.tokenParsed?.session_state);
                localStorage.setItem("isLogin", true);
                localStorage.setItem("user", keycloak?.tokenParsed?.preferred_username);
                localStorage.setItem("refreshToken", keycloak?.refreshToken);
                localStorage.setItem("firstname", keycloak?.idTokenParsed.given_name);
                localStorage.setItem("lastname", keycloak?.idTokenParsed?.family_name);
                localStorage.setItem("userid", res?.data?.uuid); 
                localStorage.setItem("ssoToken", res?.data?.token);
                localStorage.setItem("company_name", res?.data?.company_name);
                localStorage.setItem("company_id", res?.data?.customers ? res?.data?.customers[0] : "null");
            }
                      const response = await authorizeWithSSO(keycloak?.token);
                         const { data } = response;
          dispatch({ type: 'LOGIN_SUCCESS', payload: data });

        })();
    }, []);
    const classes = useStyles();
    const history = useHistory();


    const [companyName, setCompanyName] = useState('');
    const [countryList, setCountryList] = useState([]);
    const [loading, setLoading] = useState(false);
    const [userDetails, setUserDetails] = useState(null);
    const [profileLoading, setProfileLoading] = useState(false);
    const [editProfileOpen, setEditProfileOpen] = useState(false);
    const [editSkillsOpen, setEditSkillsOpen] = useState(false);
    const [skillsLoading, setSkillsLoading] = useState(false);
    const [formState, setFormState] = useState({ ...initFormState });
    const [skills, setSkills] = React.useState('');

    // useEffect(() => {
    //     getUserProfile();
    // }, []);
useEffect(() => {
    const interval = setInterval(() => {
        const firstname = localStorage.getItem("firstname");
        if (firstname) {
            getUserProfile();
            clearInterval(interval)
        }
    }, 1000);

    return () => clearInterval(interval);
}, []);

    async function getUserProfile() {
        setProfileLoading(true);
        const user = await userServices.getDetailMyProfile();
        if (user && user.data && user.data.length) {
            const userData = user.data[0];
            setUserDetails(userData);
            setSkills(userData?.skills);
            setFormState({
                firstName: userData.firstName,
                lastName: userData.lastName,
                userName: userData.userName,
                email: userData.email,
                member_since: userData.member_since,
                profile_image: userData.profile_image,
                country: userData.country,
                secure_connection: false,
                use_authentication: false,
                contact_no: userData?.contact_no || "",
                address: userData?.address || "",
                city: userData?.city || "",
                province: userData?.province || "",
                occupation: userData?.occupation || "",
                industry: userData?.industry || "",
                educationLevel: userData?.educationLevel || "",
                language: userData?.language || "",
                marketingPreferences: userData?.marketing_preferences || "",
                referralSource: userData?.referral_source || "",
                dateOfBirth: userData?.date_of_birth || "",
                pincode: userData?.pincode || ""
            });
            const customer = user.data[0].customers && user.data[0].customers[0] ? user.data[0].customers[0].company_name : '';
            setCompanyName(customer);
        }
        setProfileLoading(false);
        const countryList = await customerServices.getCounties();
        setCountryList(countryList?.country_list || []);
    }

    const handleInputChange = (evt) => {
        const { name, value } = evt.target;
        setFormState({ ...formState, [name]: value });
    }

    const formValidationCheck = () => {
        if (!formState.firstName || !formState.lastName || !formState.userName || !formState.email || !formState.country || !formState.city || !formState.province || !formState.address || !formState.contact_no) {
            notify("error", REQUIRED_ERROR);
            return null;
        }
        return handleFormSubmit();
    }

    const handleFormSubmit = async (newData) => {
        setLoading(true);
        const payload = newData ? { ...formState, ...newData } : { ...formState };
        let result = await userServices.updateProfile({ ...payload, marketing_preferences: payload?.marketingPreferences || null, referral_source: payload?.referralSource || null, date_of_birth: payload?.dateOfBirth || null });
        if (result && result.status) {
            setLoading(false);
            notify("success", result.message);
            if (!newData) {
                setEditProfileOpen(false);
                getUserProfile();
            }
        } else {
            notify("error", result && result.message ? result.message : 'Something Went Wrong')
            setLoading(false)
        }
    }

    const handleSkillsSubmit = async (newData) => {
        if (!newData.skills) {
            notify("error", 'Please enter your skills!');
            return null;
        }
        setSkillsLoading(true);
        const payload = newData ? { ...formState, ...newData } : { ...formState };
        let result = await userServices.updateProfile(payload);
        if (result && result.status) {
            notify("success", "Skills updated succesfully");
            getUserProfile();
        } else {
            notify("error", result && result.message ? result.message : 'Something Went Wrong')
        }
        setEditSkillsOpen(false);
        setSkillsLoading(false)
    }

    const handleUploadImage = (e) => {
        let file = e.target.files[0];
        if (!file) return;
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onloadend = () => {
            setFormState({ ...formState, profile_image: reader.result })
            handleFormSubmit({ profile_image: reader.result });
        };
    }

    const handleChange = (value, field) => {
        setFormState({ ...formState, [field]: value });
    }

    const handleItemClick = async (path) => {
        if (path === 'logout') {
            await logout(dispatch);
            window.location.href = Paths.MainPage;
        } else {
            history.push({ pathname: path });
        }
    }

    const handleProfileChange = (evt) => {
        document.getElementById('icon-button-file').click();
    }

    return (
      
            <main>
                <Container className={classes.content}>
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={12} lg={3}>
                            <Card style={{ background: '#F7F9FA', marginBottom: 20 }}>
                                <CardContent style={{ textAlign: 'center' }}>
                                    <div className={classes.icon}>
                                        <img
                                            alt="logo"
                                            width='100%'
                                            className={classes.imgIcon}
                                            src={formState.profile_image ? formState.profile_image : userDefaultImage}
                                        />
                                    </div>
                                    <Typography style={{ fontSize: 20, fontWeight: 700, lineHeight: '24px', color: '#4D5766' }} variant="subtitle2" className={classes.profileTitle} >
                                        {userDetails?.firstName}
                                    </Typography>
                                    <Typography variant="subtitle2" className={classes.profileTitle} >
                                        @{userDetails?.lastName}
                                    </Typography>
                                    <Typography variant="subtitle2" className={classes.profileTitle} >
                                        <PublicIcon /> {userDetails?.country}
                                    </Typography>
                                    <input style={{ display: "none" }} accept="image/*" onChange={handleUploadImage} id="icon-button-file" type="file" />
                                    <Button onClick={handleProfileChange} style={{ marginTop: 10, borderRadius: 5 }} color="primary" variant="contained"> Upload Photo </Button>
                                </CardContent>
                            </Card>
                            {/* <Card style={{ background: '#F7F9FA', marginBottom: 20 }}>
                                <div className={classes.sidebarContainer}>
                                    <ProfileSidebar menuItems={profileMenus} handleItemClick={handleItemClick} />
                                </div>
                            </Card> */}
                        </Grid>
                        <Grid item xs={12} sm={12} lg={9}>
                            {profileLoading ?
                                <div style={{ textAlign: 'center' }}><CircularProgress /></div>
                                :
                                <>
                                    <Card style={{ background: '#F7F9FA', marginBottom: 20, minHeight: 350 }}>
                                        <CardContent>
                                            <Typography
                                                variant="h4"
                                                style={{ marginBottom: 15 }}
                                                className={classes.heading}
                                                color="textPrimary">
                                                Personal Details
                                                <IconButton onClick={evt => setEditProfileOpen(true)} style={{ float: 'right', borderRadius: '50%', background: '#4D5766', color: '#fff' }}><EditIcon /></IconButton>
                                            </Typography>
                                            <Grid container spacing={2}>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: 10, marginTop: 20, color: '#4D5766' }}>
                                                        First Name
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        value={formState.firstName}
                                                        type={'text'}
                                                        readOnly
                                                        disabled
                                                        name="firstName"
                                                        className={classes.inputStyle}
                                                        placeholder={"First Name"}
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        color={'secondary'}
                                                        onChange={handleInputChange}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: 10, marginTop: 20, color: '#4D5766' }}>
                                                        Last Name
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        readOnly
                                                        disabled
                                                        value={formState.lastName}
                                                        type={'text'}
                                                        className={classes.inputStyle}
                                                        placeholder={"Last Name"}
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        name="lastName"
                                                        color={'secondary'}
                                                        onChange={handleInputChange}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: 10, marginTop: 20, color: '#4D5766' }}>
                                                        Email
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        value={formState.email}
                                                        readOnly
                                                        disabled
                                                        type={'text'}
                                                        className={classes.inputStyle}
                                                        placeholder={"Email"}
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        name="lastName"
                                                        color={'secondary'}
                                                    />
                                                </Grid>
                                            </Grid>
                                            <Grid container spacing={2}>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: 10, marginTop: 20, color: '#4D5766' }}>
                                                        Phone Number
                                                    </Typography>
                                                    {/* <PhoneInputField
                                                        value={(formState?.contact_no) ? formState?.contact_no?.toString() : ""}
                                                        name="contact_no"
                                                        disabled
                                                    /> */}
                                                    <PhoneInputField
                                                        // handleChange={(newVal) => setFormState({...formState, contact_no: newVal})}
                                                        value={(formState.contact_no) ? formState.contact_no?.toString() : ""}
                                                        customClass={classes.phoneInputStyle}
                                                        name="contact_no"
                                                        disabled={true}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: '20px', marginTop: '15px' }}>
                                                        Country *
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        value={formState.country}
                                                        readOnly
                                                        disabled
                                                        type={'text'}
                                                        className={classes.inputStyle}
                                                        placeholder={"Country"}
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        color={'secondary'}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: '20px', marginTop: '15px' }}>
                                                        City *
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        value={formState.city}
                                                        readOnly
                                                        disabled
                                                        type={'text'}
                                                        className={classes.inputStyle}
                                                        placeholder={"City"}
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        color={'secondary'}
                                                    />
                                                </Grid>
                                            </Grid>
                                            <Grid container spacing={2}>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: '20px', marginTop: '15px' }}>
                                                        Province *
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        readOnly
                                                        value={formState.province}
                                                        type={'text'}
                                                        name="province"
                                                        className={classes.inputStyle}
                                                        placeholder={"Province"}
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        color={'secondary'}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: '20px', marginTop: '15px' }}>
                                                        Address *
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        readOnly
                                                        value={formState.address}
                                                        type={'text'}
                                                        className={classes.inputStyle}
                                                        placeholder={"Address"}
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        name="address"
                                                        color={'secondary'}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: '20px', marginTop: '15px' }}>
                                                        Date Of Birth *
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        value={formState.dateOfBirth}
                                                        type={'date'}
                                                        className={classes.inputStyle}
                                                        readOnly
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        color={'secondary'}
                                                        name="dateOfBirth"
                                                    />
                                                </Grid>
                                            </Grid>
                                            <Grid container spacing={2}>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: 20, marginTop: '15px' }}>
                                                        Industry
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        value={formState.industry}
                                                        readOnly
                                                        disabled
                                                        type={'text'}
                                                        className={classes.inputStyle}
                                                        placeholder={"Industry"}
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        color={'secondary'}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: 20, marginTop: '15px' }}>
                                                        Preferred Language
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        value={formState.language}
                                                        readOnly
                                                        disabled
                                                        type={'text'}
                                                        className={classes.inputStyle}
                                                        placeholder={"Preferred Language"}
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        color={'secondary'}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: 20, marginTop: '15px' }}>
                                                        Marketing Preferences
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        value={formState.marketingPreferences}
                                                        readOnly
                                                        disabled
                                                        type={'text'}
                                                        className={classes.inputStyle}
                                                        placeholder={"Marketing Preferences"}
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        color={'secondary'}
                                                    />
                                                </Grid>
                                            </Grid>
                                            <Grid container spacing={2}>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: 20, marginTop: '15px' }}>
                                                        Referral Source
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        value={formState.referralSource}
                                                        readOnly
                                                        disabled
                                                        type={'text'}
                                                        className={classes.inputStyle}
                                                        placeholder={"Referral Source"}
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        color={'secondary'}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: '20px', marginTop: '15px' }}>
                                                        Occupation
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        readOnly
                                                        value={formState.occupation}
                                                        type={'text'}
                                                        placeholder={"Occupation"}
                                                        className={classes.inputStyle}
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        color={'secondary'}
                                                        name="occupation"
                                                    />
                                                </Grid>
                                                <Grid item xs={12} sm={6} lg={4}>
                                                    <Typography
                                                        variant="subtitle2"
                                                        color="textPrimary"
                                                        style={{ marginBottom: '20px', marginTop: '15px' }}>
                                                        Postal Code
                                                    </Typography>
                                                    <Input
                                                        disableUnderline
                                                        readOnly
                                                        value={formState.pincode}
                                                        type={'text'}
                                                        placeholder={"Postal Code"}
                                                        className={classes.inputStyle}
                                                        style={{ width: '100%', minWidth: 180 }}
                                                        color={'secondary'}
                                                        name="pincode"
                                                    />
                                                </Grid>
                                            </Grid>
                                        </CardContent>
                                    </Card>
                                    <Card style={{ background: '#F7F9FA', minHeight: 120, marginBottom: 20 }}>
                                        <CardContent>
                                            <Grid container justifyContent="space-between"  >
                                                <Grid item>
                                                    <Typography
                                                        variant="h4"
                                                        className={classes.heading}
                                                        color="textPrimary">
                                                        Skills
                                                    </Typography>
                                                </Grid>
                                                <Grid item>
                                                    <IconButton onClick={() => setEditSkillsOpen(true)}>
                                                        <EditIcon />
                                                    </IconButton>
                                                </Grid>
                                            </Grid>
                                            <Typography>{userDetails?.skills || "-"}</Typography>
                                        </CardContent>
                                    </Card>
                                </>}
                        </Grid>
                    </Grid>
                </Container>
                <EditProfileModal
                    open={editProfileOpen}
                    handleClose={evt => {
                        setEditProfileOpen(false)
                    }}
                    handleSubmit={formValidationCheck}
                    formState={formState}
                    handleInputChange={handleInputChange}
                    companyName={companyName}
                    countryList={countryList}
                    handleAutoSelectAddress={(addressData) => setFormState({ ...formState, ...addressData })}
                    handleChange={handleChange}
                    loading={loading}
                />
                <EditSkillsModal
                    open={editSkillsOpen}
                    handleClose={(evt) => {
                        setEditSkillsOpen(false)
                    }}
                    skills={skills}
                    handleChange={(skill) => setSkills(skill)}
                    handleSubmit={handleSkillsSubmit}
                    loading={skillsLoading}
                />
            </main>
          
     
    );
}

export default StartLearningProfile;