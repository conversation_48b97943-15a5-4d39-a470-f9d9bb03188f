import React from 'react';
import clsx from 'classnames'
import { ListItem, ListItemIcon, ListItemText, Icon, Button, makeStyles } from '@material-ui/core';
import { NavLink } from 'react-router-dom';

const useStyles = makeStyles(theme => {
  const transition = theme.transitions.create(['background', 'color'], {duration: 500, easing: theme.transitions.easing.easeInOut})
  return {
  item:{
    padding: 0
  },
  icon:{
    padding: theme.spacing(0.3),
    marginRight: theme.spacing(1)
  },
  root: {
    backgroundColor: '#999999',
    color: 'white',
    width: '100%',
    transition,
    '&:hover, $:hover':{
      backgroundColor: theme.palette.grey['A100'],
      transition,
    }
  },
  selected: {
    backgroundColor: theme.palette.success.dark,
    color: 'white',
    transition,
    '&:hover, $:hover':{
      transition,
      backgroundColor: theme.palette.success.main,
    }
  },
  active: {
    backgroundColor: '#002288',
    color: 'white',
    transition,
    '&:hover, $:hover':{
      backgroundColor: theme.palette.primary.light,
      transition,
    }
  }
}
});

export const NavItem =  ({ icon = null, children = 'Home', classes={
  link:'nav-link',
  icon:'nav-link-icon',
  text:'nav-link-text',
  selected:'nav-link'
},selected, active, ...props }) => (
    <ListItem component={Button} {...props} className={clsx(classes.link, {

    })} button>
      {icon && <ListItemIcon className={'nav-link-icon'}>
        {icon.indexOf(' ') === -1 ? (
          <Icon>{icon}</Icon>
        ) : (
          <i className={icon} />
        )}
      </ListItemIcon>}
      <ListItemText className={'nav-link-text'}>{children}</ListItemText>
    </ListItem>
  )

const NavButton = ({
  title = 'Home',
  path='/',
  icon = null,
  component,
  selected =false,
  navigate = false,
  ...props
}) =>{ 
  const classes = useStyles();
  const startIcon=(<Icon size="small" className={classes.icon}>{icon}</Icon>)
//   const bprops = mergeDeep(icon ? {startIcon}: {}, navigate ? {onClick: ()=>navigate(path), to: path} : {to: path})
  return (
    <ListItem className={classes.item} disableGutters>
    <Button
      activeClassName={classes.active}
      className={clsx(classes.root, {
        [classes.selected]: selected
      })}
      component={NavLink}
    //   {...bprops}
     >
      {title}
    </Button>
  </ListItem>
)};
export default NavButton