import React, { useEffect } from 'react';
import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';
import Layout from "../../Components/layouts";
import { CardContent, Container, Typography } from '@material-ui/core';
import { useHistory } from 'react-router';
import variables from '../../Sys/variable.scss';
import ProfileSidebar from '../../Components/ProfileSidebar';
import Card from '@material-ui/core/Card';
import { profileMenus } from './index';
import userDefaultImage from 'Assets/images/user-default-image.png';
import PublicIcon from '@material-ui/icons/Public';
import { UserServices } from 'Services/UserServices';
import { REQUIRED_ERROR } from 'utils/constants';
import { notify } from '../../utils';
import Input from '@material-ui/core/Input';
import Button from '@material-ui/core/Button';
import VisibilityOffIcon from '@material-ui/icons/VisibilityOff';
import CircularProgress from '@material-ui/core/CircularProgress';
import VisibilityIcon from '@material-ui/icons/Visibility';
import { InputAdornment } from '@material-ui/core';
import { logout, useAuthDispatch } from 'Context';
import { Paths } from 'Routes/routePaths';


const useStyles = makeStyles((theme) => ({
    content: {
        minHeight: 305,
        maxWidth: 1040,
        marginTop: 20
    },
    inputStyle: {
        borderColor: '#fff',
        background: '#fff',
        borderRadius: '25px',
        color: '#989898'
    },
    contentWrap: {
        width: '100%'
    },
    pageHeader: {
        display: 'flex',
        justifyContent: 'space-between',
        margin: '25px 0'
    },
    heading: {
        color: variables.labelcolor,
        fontSize: '23px',
        fontWeight: '500'
    },
    btn: {
        padding: '13px 35px',
        fontSize: '13px',
        color: 'white',
        borderRadius: '5px',
        marginLeft: '15px',
    },
    cancelBtn: {
        padding: '13px 35px',
        background: '#B7B7B7',
        fontSize: '13px',
        color: 'white',
        borderRadius: '5px',
        marginLeft: '15px',
        "&:hover": {
            background: "#454343"
        }
    },
    placeBtn: {
        display: 'flex',
        justifyContent: 'end',
        padding: '20px 0',
        marginTop: 20
    },
    imgIcon: {
        background: '#090909',
        borderRadius: '50%',
    },
    input: {
        display: 'none'
    },
    icon: {
        position: 'relative',
        margin: '1px auto 24px',
        borderRadius: '50%',
        maxWidth: 136,
        textAlign: 'center'
    },
    iconStyle: {
        borderRadius: '50%',
        color: '#000',
        background: '#fff',
        border: '1px solid #808080'
    },
    iconbtn: {
        bottom: '-17px',
        left: '51px',
        position: 'absolute'
    },
    sidebarContainer: {
        marginBottom: 20
    },
    profileContainer: {
        background: '#fff'
    },
    profileTitle: {
        fontSize: 14,
        paddingBottom: 10,
        '& .MuiSvgIcon-root': {
            position: 'relative',
            top: 6
        }
    }
}));

const userServices = new UserServices();

const initFormState = { old_password: "", new_password: "", confirm_password: "" };

const OrderHistory = () => {

    const classes = useStyles();
    const history = useHistory();
    const dispatch = useAuthDispatch();
    
    const [loading, setLoading] = React.useState(false);
    const [userDetails, setUserDetails] = React.useState(null);
    const [showOldText, setShowOldText] = React.useState(false)
    const [showNewText, setShowNewText] = React.useState(false)
    const [showConfirmText, setShowConfirmText] = React.useState(false)
    const [formState, setFormState] = React.useState({ ...initFormState });

    useEffect(() => {
        getUserProfile();
    }, []);

    async function getUserProfile() {
        const user = await userServices.getDetailMyProfile();
        if (user && user.data && user.data.length) {
            const userData = user.data[0];
            setUserDetails(userData);
        }
    }
    
    const handleItemClick = async (path) => {
        if(path === 'logout'){
            await logout(dispatch);
            window.location.href = Paths.MainPage;
        }else{
            history.push({ pathname: path });
        }
    }

    const handleInputChange = (evt) => {
        const { name, value } = evt.target;
        setFormState({ ...formState, [name]: value });
    }

    const formValidationCheck = () => {
        if (!formState.old_password || !formState.new_password || !formState.confirm_password) {
            notify("error", REQUIRED_ERROR);
            return null;
        }
        if (formState.new_password.length < 8 || formState.confirm_password.length < 8) {
            notify("error", 'New Password and Confirm Password must be 8 character long')
            return null
        }
        if (formState.new_password !== formState.confirm_password) {
            notify("error", 'New Password and Confirm Password must be same')
            return null
        }
        return handleFormSubmit(formState);
    }

    const handleFormSubmit = async (formState) => {
        setLoading(true)
        await userServices.changePassword(formState).then(
            data => {
                if (data && data.status) {
                    setLoading(false)
                    notify("success", data.message)
                    setFormState({ ...initFormState })
                }
            },

            error => {
                notify("error", error.response.data.message ? error.response.data.message : error.message)
                setLoading(false)
            }
        )
    }

    const handleIconClick = (type) => {
        if (type === "old") {
            setShowOldText(!showOldText)
        } else if (type === "new") {
            setShowNewText(!showNewText)
        } else {
            setShowConfirmText(!showConfirmText)
        }
    }


    return (
        <Layout>
            <main>
                <Container className={classes.content}>
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={12} lg={3}>
                            <Card style={{ background: '#F7F9FA', marginBottom: 20 }}>
                                <CardContent style={{ textAlign: 'center' }}>
                                    <div className={classes.icon}>
                                        <img
                                            alt="logo"
                                            width='100%'
                                            className={classes.imgIcon}
                                            src={userDetails?.profile_image ? userDetails?.profile_image : userDefaultImage}
                                        />
                                    </div>
                                    <Typography style={{ fontSize: 20, fontWeight: 700, lineHeight: '24px', color: '#4D5766' }} variant="subtitle2" className={classes.profileTitle} >
                                        {userDetails?.firstName}
                                    </Typography>
                                    <Typography variant="subtitle2" className={classes.profileTitle} >
                                        @{userDetails?.lastName}
                                    </Typography>
                                    <Typography variant="subtitle2" className={classes.profileTitle} >
                                        <PublicIcon /> {userDetails?.country}
                                    </Typography>
                                </CardContent>
                            </Card>
                            <Card style={{ background: '#F7F9FA', marginBottom: 20 }}>
                                <div className={classes.sidebarContainer}>
                                    <ProfileSidebar menuItems={profileMenus} handleItemClick={handleItemClick} />
                                </div>
                            </Card>
                        </Grid>
                        <Grid item xs={12} sm={12} lg={9}>
                            <Card style={{ background: '#F7F9FA', marginBottom: 20, minHeight: 350 }}>
                                <CardContent>
                                    <Typography style={{ color: '#4D5766', fontSize: 20, fontWeight: 700, lineHeight: '20px' }}>Change Password</Typography>
                                    <Grid container style={{ marginBottom: '15px', marginTop: '15px' }}>
                                        <Grid item xs={12} sm={12} lg={6}>
                                            <Typography
                                                variant="subtitle2"
                                                style={{ color: '#4D5766', marginBottom: '20px', marginTop: '15px' }}>
                                                Old Password
                                            </Typography>
                                            <Input 
                                                disableUnderline                                    
                                                value={formState.old_password}
                                                type={showOldText ? 'text' : 'password'}
                                                name="old_password"
                                                className={classes.inputStyle}
                                                placeholder={"Old Password"}
                                                style={{ minWidth: 220, width: '100%' }}
                                                color={'secondary'}
                                                onChange={handleInputChange}
                                                endAdornment={
                                                    <InputAdornment onClick={e => handleIconClick("old")} position="start">
                                                        {showOldText ? <VisibilityIcon /> : <VisibilityOffIcon />}
                                                    </InputAdornment>
                                                }
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={12} lg={6}>
                                            <Typography
                                                variant="subtitle2"
                                                color="textPrimary"
                                                style={{ marginBottom: '20px', marginTop: '15px', color: variables.labelcolor }}>
                                                New Password
                                            </Typography>
                                            <Input 
                                                disableUnderline
                                                value={formState.new_password}
                                                type={showNewText ? 'text' : 'password'}
                                                className={classes.inputStyle}
                                                placeholder={"New Password"}
                                                style={{ minWidth: 220, width: '100%' }}
                                                name="new_password"
                                                color={'secondary'}
                                                onChange={handleInputChange}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={12} lg={6}>
                                            <Typography
                                                variant="subtitle2"
                                                color="textPrimary"
                                                style={{ marginBottom: '20px', marginTop: '15px', color: variables.labelcolor }}>
                                                Confirm Password
                                            </Typography>
                                            <Input 
                                                disableUnderline                                    
                                                value={formState.confirm_password}
                                                type={showConfirmText ? 'text' : 'password'}
                                                placeholder={"Confirm Password"}
                                                className={classes.inputStyle}
                                                style={{ minWidth: 220, width: '100%' }}
                                                color={'secondary'}
                                                name="confirm_password"
                                                onChange={handleInputChange}
                                                endAdornment={
                                                    <InputAdornment onClick={e => handleIconClick("confirm")} position="start">
                                                        {showConfirmText ? <VisibilityIcon /> : <VisibilityOffIcon />}
                                                    </InputAdornment>
                                                }
                                            />
                                        </Grid>
                                    </Grid>
                                    <div className={classes.placeBtn}>
                                        <Button color="primary" variant="outlined" className={classes.btn}
                                            onClick={formValidationCheck}>
                                            {loading ? <CircularProgress size={20} color="secondary" /> : 'Update'}
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </Grid>
                    </Grid>
                </Container>
            </main>
        </Layout>
    );
}

export default OrderHistory;