import { Swiper, SwiperSlide } from 'swiper/react';
import SwiperCore, { A11y, Keyboard, Navigation } from 'swiper';
import 'swiper/css';
import { <PERSON><PERSON>, Divider, IconButton, Typography, makeStyles } from '@material-ui/core';
import { ArrowBackIos, ArrowForwardIos } from '@material-ui/icons';
import './PopularCarousel.scss';
import { useRef } from 'react';
import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';
import CardMedia from '@material-ui/core/CardMedia';
import { getCourseName } from 'Components/layouts/Header';
import ArrowForwardIcon from '@material-ui/icons/ArrowForward';
import { IsMobileDevice } from 'utils';

const FOCUSABLE_ELEMENT_SELECTORS = [
    'a:not([disabled])',
    'button:not([disabled])',
    'input[type=text]:not([disabled])',
    '[tabindex]:not((Idisabled]):not([tabindexe"-1"))'
];

SwiperCore.use([Navigation, Keyboard, A11y]);
let swiperCounter = 1;

const useStyles = makeStyles({
    root: {
        minHeight: 370,
        marginBottom: 10,
        boxShadow: 'none',
        backgroundColor: '#FAFCFF',
        borderRadius: 8,
        "& .learnMoreBtn": {
            marginTop: 20,
            marginBottom: 20,
            color: "#0080CA",
            float: 'right'
        }
    },
    brnadLogoCard: {
        background: "none",
        boxShadow: 'none',
        minHeight: 150,
    },
    media: {
        height: 200,
    },
    cover: {
        width: "100%",
        maxHeight: 100,
        borderRadius: 8
    }
});

export const handleSwiperSlides = (data) => {
    const { slides, visibleSlides } = data || {};
    if (!slides || !visibleSlides) {
        return null;
    }
    slides.forEach((item) => {
        item.setAttribute('aria-hidden', 'true');
        const focusableElements = item.querySelectorAll(...FOCUSABLE_ELEMENT_SELECTORS);
        focusableElements.forEach((item) => item.setAttribute('tabindex', '-1'));
    });
    visibleSlides.forEach((item) => {
        item.setAttribute('aria-hidden');
        const focusableElements = item.querySelectorAll(...FOCUSABLE_ELEMENT_SELECTORS);
        focusableElements.forEach((item) => item.setAttribute('tabindex'));
    });
}

const ProductCard = ({ itm, handleClickItem, type = null }) => {
    const classes = useStyles();
    return (<Card className={classes.root}>
        <CardMedia
            onClick={(evt) => handleClickItem ? handleClickItem(itm) : null}
            className={classes.media}
            image={itm.image}
            style={{ cursor: 'pointer' }}
            title="Contemplative Reptile"
        />
        <CardContent>
            <Typography style={{ cursor: 'pointer' }} onClick={(evt) => handleClickItem ? handleClickItem(itm) : null} variant="h4">
                {getCourseName(itm.name)}
            </Typography>
            <Typography style={{ paddingTop: 10, paddingBottom: 20 }} title={itm?.fullDesc} variant="body2" color="textSecondary" component="p">
                {itm.desc}
            </Typography>
            <Divider />
            <Button onClick={(evt) => handleClickItem ? handleClickItem(itm) : null} className="learnMoreBtn" >Learn More <ArrowForwardIcon style={{width: 15, marginLeft: 5}} /> </Button>
        </CardContent>
    </Card>);
}

export default function PopularCoursesCarousel({ list, handleClickItem }) {

    swiperCounter += 1;
    const carouselRef = useRef();
    const isMobile = IsMobileDevice('sm');

    return (
        <div className='Popular__Carousel'>
            <div style={(!isMobile) ? {display: 'none'} : null} className={`Popular__Navigation Popular__Navigation-${swiperCounter}`}>
                <IconButton className='Popular__Navigation__button Popular__Navigation__button--back'>
                    <ArrowBackIos />
                </IconButton>
                <IconButton className='Popular__Navigation__button Popular__Navigation__button--forward'>
                    <ArrowForwardIos />
                </IconButton>
            </div>
            <Swiper
                ref={carouselRef}
                freeMode={{
                    minimumVelocity: 0.01,
                    momentum: true,
                    momentumBounce: true,
                    momentumBounceRatio: 1.2,
                    momentunRatio: 1.2,
                    momentumVelocityRatio: 1.2,
                    sticky: true
                }}
                watchoverflow={true}
                watchSLidesProgress={true}
                watchSlidesVisibility={true}
                preloadImages={true}
                spaceBetween={30}
                navigation={{
                    prevEl: `.Popular__Navigation-${swiperCounter} .Popular__Navigation__button--back`,
                    nextEl: `.Popular__Navigation-${swiperCounter} .Popular__Navigation__button--forward`,
                    disabledClass: `Popular__Navigation__button--disabled`,
                    hiddenClass: `Popular__Navigation__button--hidden`
                }}
                onSwiper={(swiper) => handleSwiperSlides(swiper)}
                onSlideChange={(swiper) => handleSwiperSlides(swiper)}
                breakpoints={{
                    1200: {
                        slidesPerView: 3,
                        slidesPerGroup: 3
                    },
                    992: {
                        slidesPerView: 2,
                        slidesPerGroup: 2
                    },
                    768: {
                        slidesPerView: 1,
                        slidesPerGroup: 1
                    },
                    300: {
                        slidesPerView: 1,
                        slidesPerGroup: 1
                    }
                }}
                grid={{
                    rows: 3
                }}
                speed={600}
                slidesPerView={3}
                className="mySwiper"
            >
                {list.map((itm, key) => (<>
                    <SwiperSlide key={key} >
                        <ProductCard handleClickItem={() => handleClickItem(itm)} itm={itm} />
                    </SwiperSlide>
                </>
                ))}
            </Swiper>
        </div>
    );
}