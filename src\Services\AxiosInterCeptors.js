import axios from 'axios';
import '../Sys/config.js';

const apiUrl = global.platformURI;
const publicToken = global.publicAPIAuthToken;

export default class AxiosInterCeptors {
    constructor() {
        axios.interceptors.request.use(
          function (config) {
            let token = localStorage.getItem('ssoToken');
            config.baseURL = apiUrl;
            config.headers.Authorization = token ? `Token ${token}` : publicToken;
            return config;
          },
          error => {
            console.log('error.response.status', error);
            return error;
          }
        );
      }
}