import { Swiper, SwiperSlide } from 'swiper/react';
import Swiper<PERSON>ore, { Keyboard, Navigation, Pagination } from 'swiper';
import { Card, CardHeader, CardContent, Avatar, Typography, IconButton } from '@material-ui/core';
import { useRef } from 'react';
import ArrowForwardIcon from '@material-ui/icons/ArrowForward';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import { handleSwiperSlides } from '..';
// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import "./styles.scss";

const ProductCard = ({ itm }) => {
  
  return (
    <Card style={{borderBottom: '6px solid #5dd0df'}}>
      <CardHeader
        avatar={
          <Avatar style={{
            background: 'black',
            height: 80,
            width: 80
          }} src={itm.img} aria-label="recipe" />
        }
        title={itm.name}
        subheader={<Typography variant='h4' style={{padding: '15px 0px'}} >{itm.designation}</Typography>}
      />
      <CardContent>
        <Typography variant="body2" color="textSecondary" component="p">
          {itm.desc}
        </Typography>
      </CardContent>
    </Card>
  )
}

SwiperCore.use( [Navigation, Keyboard, Pagination] );
let swiperCounter = 1;

export default function TestimonialsCarousel({ list }) {
  
  swiperCounter += 1;
  const carouselRef = useRef();
  
  return (
    <div className='Testimonial__Carousel'>
      <Swiper
        ref={ carouselRef }
        watchoverflow={true}
        watchSLidesProgress={ true }
        watchSlidesVisibility={ true }
        preloadImages={true}
        spaceBetween={20}
        navigation={{
          prevEl: `.Testimonial__Navigation-${swiperCounter} .Testimonial__Navigation__button--back`,
          nextEl: `.Testimonial__Navigation-${swiperCounter} .Testimonial__Navigation__button--forward`,
          disabledClass: `Testimonial__Navigation__button--disabled`,
          hiddenClass: `Testimonial__Navigation__button--hidden`
        }}
        pagination={{
          clickable: true,
          type: 'progressbar'
        }}
        speed={600}
        slidesPerView={1}
        breakpoints={{
          1200: {
            slidesPerView: 2,
            slidesPerGroup: 2
          },
          992: {
            slidesPerView: 2,
            slidesPerGroup: 2
          },
          768: {
            slidesPerView: 1,
            slidesPerGroup: 1
          }
        }}
        onSwiper={ (swiper) => handleSwiperSlides( swiper ) }
        onSlideChange={ (swiper) => handleSwiperSlides( swiper ) }
        className="mySwiper"
      >
        {list.map((itm, key) => (
          <SwiperSlide key={key}>
            <ProductCard itm={itm} />
          </SwiperSlide>
        ))}
      </Swiper>
      <div className={`Testimonial__Navigation Testimonial__Navigation-${swiperCounter}`}>
        <IconButton className='Testimonial__Navigation__button Testimonial__Navigation__button--back'>
          <ArrowBackIcon />
        </IconButton>
        <IconButton className='Testimonial__Navigation__button Testimonial__Navigation__button--forward'>
          <ArrowForwardIcon />
        </IconButton>
      </div>
    </div>
  );
}