import React, { Fragment } from 'react';
import {Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import logo from 'Assets/images/logo.jpg';
import { COMPANY_DETAILS } from 'utils/constants';

const styles = StyleSheet.create({
    container: {
        marginTop: 0,
        marginBottom: 0,
        paddingBottom: 0,
        flexDirection: 'row',
        '@media max-width: 400': {
            flexDirection: 'column',
        },
    },
    leftColumn: {
        flexDirection: 'column',
        width: 360,
        paddingTop: 30,
        '@media max-width: 400': {
          width: '100%',
          paddingRight: 0,
        },
        '@media orientation: landscape': {
          width: 200,
        },
    },
    rightContainer: {
        flexDirection: 'column',
        paddingTop: 30,
        paddingLeft: 0,
        paddingBottom: 0,
        '@media max-width: 400': {
            paddingTop: 10,
            paddingLeft: 0,
        },
    },
    logo: {
        width: 140, 
        height: 85
    }
});
const InvoiceNo = () => (
    <Fragment>
        <View style={styles.container}>
            <View style={styles.leftColumn}>
                <Image
                    src={logo}
                    style={styles.logo}
                />
            </View>
            <View style={styles.rightContainer}>
                <Text>{COMPANY_DETAILS.name}</Text>
                <Text>{COMPANY_DETAILS.address}</Text>
                <Text>{COMPANY_DETAILS.address2}</Text>
                <Text>{COMPANY_DETAILS.phoneNo}</Text>
                <Text>{COMPANY_DETAILS.email}</Text>
                <Text>{COMPANY_DETAILS.vatRegistrationNo}</Text>
                <Text>{COMPANY_DETAILS.businessIdNo}</Text>
            </View>
        </View>
    </Fragment>
);
export default InvoiceNo