// import logoDeviare from 'Assets/images/adcorp-logo.png';
import logoDeviare from 'Assets/images/deviare-logo-light.svg';

const logo_src =
  localStorage.getItem('company_logo') || process.env.REACT_APP_LOGO;
const loader_src =
  localStorage.getItem('company_loader') || process.env.REACT_APP_LOADING_IMAGE;
const profile_src =
  localStorage.getItem('profileImage') ||
  process.env.REACT_APP_DEFAULT_PROFILE_IMAGE;
const fetchData = (
  url, result_fn = v => v,
  _def=false,
  options = {
    method: 'GET',
    mode: 'no-cors'
  }
) => {
    var done = false;
  const inner = async () => {
    try {
      const res = await fetch(url, options);

      const mimetype = res.headers.get('Content-Type') || 'image/jpeg';
      const image = await res.blob();
      result_fn( { data: btoa(image), mimetype })
    } catch (error) {
      console.log(error);
    }
    result_fn(_def);
  };
  return inner().catch((error) => {console.log(error); result_fn(_def);})
};
window.fetchData = fetchData;
export default logoDeviare;
