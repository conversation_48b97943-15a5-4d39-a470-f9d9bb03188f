import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Toolbar from '@material-ui/core/Toolbar';
import Typography from '@material-ui/core/Typography';
import ChevronRight from '@material-ui/icons/ChevronRight';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router';

const useStyles = makeStyles(theme => ({
    breadCrumbBar: {
        padding: 0
    },
    primaryOptions: {
        cursor: 'pointer',
        color: '#fff',
        fontWeight: 'normal'
    },
    chevron: {
        color: '#FFF',
        marginLeft: theme.spacing(1),
        marginRight: theme.spacing(1)
    }
}));

function Breadcrumb({ breadcrumbList, history }) {
    const classes = useStyles();

    const handleItemClick = (item, index) => {
        let breadCrumbValLength = breadcrumbList.length - 1;
        if (breadCrumbValLength - index > 0) {
            history.go(index - breadCrumbValLength);
        }
    };

    return (
        <Toolbar className={classes.breadCrumbBar}>
            {breadcrumbList &&
                breadcrumbList.map((item, index) => (
                    <React.Fragment key={`bread-${index}`}>
                        <Typography
                            variant={'subtitle2'}
                            onClick={() => handleItemClick(item, index)}
                            className={classes.primaryOptions}>
                            {item.label}
                        </Typography>
                        {index !== breadcrumbList.length - 1 && (
                            <ChevronRight
                                color="disabled"
                                className={clsx([classes.chevron])}
                            />
                        )}
                    </React.Fragment>
                ))}
        </Toolbar>
    );
}

Breadcrumb.propTypes = {
    breadcrumbList: PropTypes.array.isRequired
};

export default withRouter(Breadcrumb);
