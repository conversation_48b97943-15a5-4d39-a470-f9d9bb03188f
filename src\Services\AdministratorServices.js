import axios from 'axios';
import AxiosInterCeptors from './AxiosInterCeptors';

export class AdministratorServices extends AxiosInterCeptors {
    
    getAllAdministrator(url) {
        return axios.get(url)
            .then(res => res.data);
    }

    addAdministrator(payload, uuid) {
        if (uuid) {
            return axios.put(`main/superadmindetail/${uuid}`, payload).then(res => res.data);
        }
        return axios.post("main/superadmincreate", payload)
            .then(res => res.data)
    }

    getAdminisratorDetail(id) {
        return axios.get(`main/superadmindetail/${id}`)
            .then(res => res.data)
    }
}