import React from 'react';
import { View, StyleSheet } from '@react-pdf/renderer';
import InvoiceTableHeader from './InvoiceTableHeader'
import InvoiceTableRow from './InvoiceTableRow'
import InvoiceTableFooter from './InvoiceTableFooter'
import moment from 'moment';
const styles = StyleSheet.create({
  tableContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 24,
  }
});

const InvoiceItemsTable = ({ invoice }) => {

  return (
    <View style={styles.tableContainer}>
      <InvoiceTableHeader row={{ title: "DATE", head1: "COURSE", head2: "DESCRIPTION", head3: "TAX", head4: "QTY", head5: "RATE" }} />
      <InvoiceTableRow items={[{
        date: invoice?.transaction_date ? moment(invoice.transaction_date).format('YYYY.MM.DD') : '-',
        course: invoice?.courseName || '-',
        description: "-",
        amount: invoice?.amount
      }]} />
      <InvoiceTableFooter currency={"ZAR"} total={invoice.amount} />
    </View>
  )
}


export default InvoiceItemsTable