import React, { useState } from 'react';
import { Typo<PERSON>, TextField, Grid, Button, Checkbox, CircularProgress, Switch } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import Layout from 'Components/layouts';

const useStyles = makeStyles((theme) => ({
  root: {
    backgroundColor: '#f9fbfd',
    minHeight: '100vh',
    padding: theme.spacing(4),
  },
  inputField: {
    marginBottom: theme.spacing(2),
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: '#b0b8c1', // always visible border
      },
      '&:hover fieldset': {
        borderColor: '#6b9cff',
      },
      '&.Mui-focused fieldset': {
        borderColor: '#6b9cff',
      },
      background: '#fff',
    },
  },
  button: {
    borderRadius: 25,
    textTransform: 'none',
    padding: theme.spacing(1.5, 4),
    fontWeight: 600,
  },
  summaryLabel: {
    fontWeight: 500,
    color: '#4d5766',
  },
  lightText: {
    color: '#999',
  },
  footerText: {
    marginTop: theme.spacing(2),
    color: '#b0b8c1',
    fontSize: 13,
    textAlign: 'center',
  },
}));

const Checkout = () => {
  const classes = useStyles();
  const [form, setForm] = useState({
    cardNumber: '',
    expiryDate: '',
    cvc: '',
    nameOnCard: '',
    billingAddress: '',
    couponCode: '',
    agreeToTerms: false,
    rememberCard: false,
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleSwitch = (e) => {
    setForm((prev) => ({ ...prev, rememberCard: e.target.checked }));
  };

  const handleSubmit = () => {
    if (!form.agreeToTerms) return;
    setLoading(true);
    // API Call logic here
  };

  return (
    <Layout>
      <div style={{ background: '#f9fbfd', minHeight: '100vh', width: '100vw', padding: 0, margin: 0 }}>
        {/* Step Progress Bar and Content Centered */}
        <div style={{ maxWidth: 700, margin: '0 auto', paddingTop: 32 }}>
          <div style={{ marginTop: 0, marginBottom: 8, fontWeight: 500, fontSize: 14 }}>Step 3 of 4</div>
          <div style={{ height: 6, background: '#e3eaf3', borderRadius: 3, marginBottom: 32, position: 'relative', overflow: 'hidden' }}>
            <div style={{ height: '100%', width: '75%', background: '#6b9cff', borderRadius: 3, transition: 'width 0.3s', position: 'absolute', left: 0, top: 0 }} />
          </div>
          {/* Main Title */}
          <Typography style={{ fontWeight: 700, fontSize: 24, textAlign: 'center', margin: '24px 0 32px 0' }} gutterBottom>
            Complete Your Subscription
          </Typography>

          {/* Order Summary */}
          <div style={{ background: '#fff', borderRadius: 16, boxShadow: '0 2px 8px rgba(0,0,0,0.05)', padding: 32, marginBottom: 32 }}>
            <Typography style={{ fontWeight: 600, fontSize: 16, marginBottom: 16 }}>Order Summary</Typography>
            <div style={{ display: 'flex', borderBottom: '1px solid #e3eaf3', paddingBottom: 16, marginBottom: 16 }}>
              <div style={{ flex: 1 }}>
                <div style={{ color: '#7a869a', fontWeight: 500, fontSize: 15 }}>Premium Plan</div>
                <div style={{ color: '#222', fontWeight: 500, fontSize: 15 }}>$19.99/month</div>
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ color: '#7a869a', fontWeight: 500, fontSize: 15 }}>Discount</div>
                <div style={{ color: '#222', fontWeight: 500, fontSize: 15 }}>- $5.00</div>
              </div>
            </div>
            <div style={{ display: 'flex', borderBottom: '1px solid #e3eaf3', paddingBottom: 16, marginBottom: 16 }}>
              <div style={{ flex: 1 }}>
                <div style={{ color: '#222', fontWeight: 700, fontSize: 16 }}>Total Due</div>
                <div style={{ color: '#222', fontWeight: 700, fontSize: 16 }}>$14.99</div>
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ color: '#7a869a', fontWeight: 500, fontSize: 15 }}>Renewal Date</div>
                <div style={{ color: '#222', fontWeight: 500, fontSize: 15 }}>Renews on July 15, 2024</div>
              </div>
            </div>
            <div>
              <span style={{ color: '#6b9cff', fontWeight: 500, fontSize: 14, cursor: 'pointer', textDecoration: 'underline' }}>Change Plan?</span>
            </div>
          </div>

          {/* Payment Information */}
          <div style={{ background: '#fff', borderRadius: 16, boxShadow: '0 2px 8px rgba(0,0,0,0.05)', padding: 32, marginBottom: 32 }}>
            <Typography style={{ fontWeight: 600, fontSize: 16, marginBottom: 16 }}>Payment Information</Typography>
            <TextField
              fullWidth
              label="Card Number"
              variant="outlined"
              name="cardNumber"
              className={classes.inputField}
              value={form.cardNumber}
              onChange={handleChange}
              placeholder="Enter cardnumber"
              InputLabelProps={{ shrink: true }}
            />
            <Grid container spacing={2} style={{ marginBottom: 0 }}>
  <Grid item xs={6}>
    <TextField
      fullWidth
      label="Expiry Date"
      variant="outlined"
      name="expiryDate"
      className={classes.inputField}
      value={form.expiryDate}
      onChange={handleChange}
      placeholder="MM/YY"
      InputLabelProps={{ shrink: true }}
    />
  </Grid>
  <Grid item xs={6}>
    <TextField
      fullWidth
      label="CVC"
      variant="outlined"
      name="cvc"
      className={classes.inputField}
      value={form.cvc}
      onChange={handleChange}
      placeholder="CVC"
      InputLabelProps={{ shrink: true }}
    />
  </Grid>
</Grid>

            <TextField
              fullWidth
              label="Name on Card"
              variant="outlined"
              name="nameOnCard"
              className={classes.inputField}
              value={form.nameOnCard}
              onChange={handleChange}
              placeholder="Enter nameoncard"
              InputLabelProps={{ shrink: true }}
            />
            <TextField
              fullWidth
              label="Billing Address"
              variant="outlined"
              name="billingAddress"
              className={classes.inputField}
              value={form.billingAddress}
              onChange={handleChange}
              placeholder="Enter billingaddress"
              InputLabelProps={{ shrink: true }}
            />
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
              <span style={{ fontSize: 15, color: '#222', marginRight: 8 }}>Remember My Card</span>
              <Switch
                checked={form.rememberCard}
                onChange={handleSwitch}
                color="primary"
                name="rememberCard"
                inputProps={{ 'aria-label': 'remember my card' }}
              />
            </div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
              <TextField
                fullWidth
                label="Coupon Code"
                variant="outlined"
                name="couponCode"
                 className={classes.inputField}
                value={form.couponCode}
                onChange={handleChange}
                placeholder="Enter couponcode"
                InputLabelProps={{ shrink: true }}
              />
              <Button style={{ minWidth: 60, height: 38, borderRadius: 16, background: '#f5f7fa', color: '#222', fontWeight: 600, boxShadow: 'none', marginLeft: 8 }} variant="contained">Apply</Button>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', margin: '24px 0 16px 0' }}>
              <Checkbox name="agreeToTerms" checked={form.agreeToTerms} onChange={handleChange} />
              <span style={{ fontSize: 14, color: '#222', marginLeft: 8 }}>
                I agree to the <a href="/terms-and-conditions" target="_blank" rel="noopener noreferrer">Terms and Conditions</a>
              </span>
            </div>
            <Button
              fullWidth
              color="primary"
              variant="contained"
              style={{ borderRadius: 25, textTransform: 'none', padding: '12px 0', fontWeight: 600, fontSize: 16, marginTop: 8 }}
              onClick={handleSubmit}
              disabled={loading || !form.agreeToTerms}
            >
              {loading ? <CircularProgress size={20} style={{ color: '#fff' }} /> : 'Confirm & Subscribe'}
            </Button>
          </div>
          <div style={{ textAlign: 'center', color: '#b0b8c1', fontSize: 13, marginTop: 16 }}>
            Transparent, secure, and simple. Your information is protected.
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Checkout;