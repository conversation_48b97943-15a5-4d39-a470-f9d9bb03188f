import React from "react";
import { Route, Redirect } from "react-router-dom";
import { useKeycloak } from '@react-keycloak/web';

const AppRoutes = ({ component: Component, path, isPrivate, ...rest }) => {
    const { keycloak, initialized } = useKeycloak();

    // For public routes, render immediately without waiting for initialization
    if (!isPrivate) {
        return <Route
            path={path}
            render={props => <Component {...props} />}
            {...rest}
        />;
    }

    // For private routes, wait for keycloak initialization
    if (!initialized) {
        return null;
    }

    return (
        <Route
            path={path}
            render={props => {
                if (!keycloak.authenticated) {
                    return <Redirect to="/" />;
                }
                return <Component {...props} />;
            }}
            {...rest}
        />
    );
};

export default AppRoutes;