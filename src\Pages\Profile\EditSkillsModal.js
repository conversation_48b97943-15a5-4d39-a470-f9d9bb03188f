import React from 'react';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import makeStyles from '@material-ui/core/styles/makeStyles';
import Grid from '@material-ui/core/Grid';
import Box from '@material-ui/core/Box';
import Typography from '@material-ui/core/Typography';
import IconButton from '@material-ui/core/IconButton';
import CircularProgress from '@material-ui/core/CircularProgress';
import CloseIcon from '@material-ui/icons/Close';
import { DialogActions, Input } from '@material-ui/core';

const useStyles = makeStyles(theme => ({
    inputStyle: {
        borderColor: '#bebaba',
        minWidth: '100%',
        borderRadius: '25px',
    },
    pageHeader: {
        display: 'flex',
        justifyContent: 'space-between',
        margin: '25px 0'
    },
    heading: {
        fontSize: '23px',
        fontWeight: '500'
    },
    btn: {
        padding: '13px 35px',
        fontSize: '13px',
        color: 'white',
        borderRadius: '5px',
        marginLeft: '15px',
    },
    cancelBtn: {
        padding: '13px 35px',
        background: '#B7B7B7',
        fontSize: '13px',
        color: 'white',
        borderRadius: '5px',
        marginLeft: '15px',
        "&:hover": {
            background: "#454343"
        }
    },
    placeBtn: {
        display: 'flex',
        justifyContent: 'end'
    },
    imgIcon: {
        width: 130,
        borderRadius: '50%',
    },
    input: {
        display: 'none'
    },
    icon: {
        position: 'relative',
        margin: '1px auto 24px',
        background: '#090909',
        borderRadius: '50%',
        maxWidth: 136,
        textAlign: 'center'
    },
    iconStyle: {
        borderRadius: '50%',
        color: '#000',
        background: '#fff',
        border: '1px solid #808080'
    },
    iconbtn: {
        bottom: '-17px',
        left: '51px',
        position: 'absolute'
    },
    closeBtn: {
        position: 'absolute',
        top: 20,
        right: 20
    }
}));

const EditSkillsModal = ({ 
    open, 
    handleClose, 
    handleSubmit, 
    loading,
    skills,
    handleChange
}) => {

    const classes = useStyles();
    
    return (
        <div>
            <Dialog
                open={open}
                style={{ borderRadius: 5 }}
                fullWidth={true}
                maxWidth={'sm'}
                onClose={handleClose}
                aria-labelledby="alert-dialog-slide-title"
                aria-describedby="alert-dialog-slide-description"
            >
                <DialogTitle id="alert-dialog-slide-title" style={{ position: 'relative' }}>
                    <Typography style={{ lineHeight: 2.5 }} variant="h1">
                        Update Skills
                    </Typography>
                    <IconButton className={classes.closeBtn} onClick={handleClose}><CloseIcon /></IconButton>
                </DialogTitle>
                <DialogContent style={{padding: 20}}>
                    <Box component="form" id="loginForm" onSubmit={handleSubmit} className={classes.formBox} >
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={12} lg={12}>
                                <Input
                                    disableUnderline
                                    value={skills}
                                    type={'text'}
                                    name="skills"
                                    className={classes.inputStyle}
                                    placeholder={"Skills"}
                                    style={{ width: '100%' }}
                                    color={'secondary'}
                                    multiline
                                    rows={2}
                                    onChange={(evt) => handleChange(evt.target.value)}
                                />
                            </Grid>
                        </Grid>
                    </Box>
                </DialogContent>
                <DialogActions>
                    <div className={classes.placeBtn}>
                        <Button onClick={handleClose} color="primary" className={classes.cancelBtn}>
                            Cancel
                        </Button>
                        <Button
                            color="primary"
                            variant="outlined"
                            className={classes.btn}
                            onClick={() => handleSubmit({ skills })}>
                            {loading ? <CircularProgress size={15} color="secondary" /> : 'Update'}
                        </Button>
                    </div>
                </DialogActions>
            </Dialog>
        </div>
    );
}

export default EditSkillsModal;