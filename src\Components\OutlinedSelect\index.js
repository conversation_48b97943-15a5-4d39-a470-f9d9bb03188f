import React from 'react';
import Select from '@material-ui/core/Select';
import MenuItem from '@material-ui/core/MenuItem';
import FormControl from '@material-ui/core/FormControl';
import makeStyles from '@material-ui/core/styles/makeStyles';
import PropTypes from 'prop-types';
import { Icon, InputLabel, Typography } from '@material-ui/core';

const useStyles = makeStyles(theme => ({
  formControl: {
    minWidth: 132
  },
  formLabel: {
    display: 'flex',
    alignItems: 'center',
    paddingLeft: 10,
    paddingTop: 10,
    paddingRight: 1,
    '&.Mui-focused': {
      paddingTop: 0
    },
    '&.MuiFormLabel-filled': {
      paddingTop: 0
    }
  },
  select: {
    padding: theme.spacing(2, 3)
  }
}));

const OutlinedSelect = ({
  val,
  name,
  variant,
  handleChange,
  handleBlur,
  options,
  placeholder,
  styleOverrides,
  disabled = false,
  selectStyle,
  multiple = false,
  menuProps = false,
  label,
  IconComponent
}) => {
  const classes = useStyles();
  const inputLabel = React.useRef(null);
  const [labelWidth, setLabelWidth] = React.useState(0);

  React.useEffect(() => {
    setLabelWidth(inputLabel.current.offsetWidth);
  }, []);

  return (
    <FormControl
      variant={variant}
      className={classes.formControl}
      disabled={disabled}
      size="small"
      style={styleOverrides}>
        <InputLabel
        ref={inputLabel}
        id={`select-outlined-${label}`}
        className={label ? classes.formLabel : ''}>
        {IconComponent && (
          <Icon
            component={() => (
              <IconComponent />
            )}
          />
        )}
        <Typography variant={'subtitle2'} color={'textPrimary'}>
          {label}
        </Typography>
      </InputLabel>
      <Select
        label={label || null}
        labelId={`label-${placeholder}`}
        id={`select-outlined-${placeholder}`}
        name={name}
        value={val}
        disableUnderline
        color="secondary"
        multiple={multiple}
        placeholder={placeholder}
        onChange={handleChange}
        onBlur={handleBlur}
        MenuProps={menuProps || menuProps}
        labelWidth={labelWidth}
        style={{
          borderColor: '#bebaba',
          borderRadius: '25px',
          padding: "6px 18px",
          ...(selectStyle && {...selectStyle})
        }}>
        <MenuItem value="" disabled={multiple}>
          <em>{placeholder || 'Select Value'}</em>
        </MenuItem>
        {options &&
          options.map((item, index) => (
            <MenuItem key={index} value={(item.id || item.uuid) || item}>
              {item.name || item}
            </MenuItem>
          ))}
      </Select>
    </FormControl>
  );
};

OutlinedSelect.prototypes = {
  val: PropTypes.any.isRequired,
  handleChange: PropTypes.func.isRequired,
  options: PropTypes.array.isRequired,
  placeholder: PropTypes.string,
  styleOverrides: PropTypes.object,
  disabled: PropTypes.bool,
  IconComponent: PropTypes.node,
  selectStyle: PropTypes.bool,
  multiple: PropTypes.bool
};

export default OutlinedSelect;
