import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Drawer from '@material-ui/core/Drawer';
import List from '@material-ui/core/List';
import ListItem from '@material-ui/core/ListItem';
import ListItemIcon from '@material-ui/core/ListItemIcon';
import ListItemText from '@material-ui/core/ListItemText';
import ArrowForwardIosIcon from '@material-ui/icons/ArrowForwardIos';
import { Paths } from 'Routes/routePaths';
import { useHistory } from 'react-router';

const useStyles = makeStyles({
    list: {
        width: '100%',
        marginBottom: 25
    },
    fullList: {
        width: 'auto',
    },
});

const DrawerList = ({ list, handleViewAllClick, toggleDrawer, isAuth }) => {
    const classes = useStyles();
    const history = useHistory();
    return (
        <div
            className={classes.list}
            role="presentation"
        >
            <List>
                {list.map((itm) => (
                    <>
                        <ListItem style={{ padding: 15 }} onClick={evt => {
                            handleViewAllClick(itm);
                            toggleDrawer();
                        }} button key={itm}>
                            <ListItemText primary={itm} />
                            <ListItemIcon>{<ArrowForwardIosIcon style={{fontSize: 16}} />}</ListItemIcon>
                        </ListItem>
                    </>
                ))}
                <ListItem style={{ padding: 15 }} onClick={evt => {
                    toggleDrawer();
                    history.push(`${Paths.Accreditations}`);
                }} button>
                    <ListItemText primary={"Accreditations"} />
                    {/* <ListItemIcon>{<ArrowForwardIosIcon style={{fontSize: 16}} />}</ListItemIcon> */}
                </ListItem>
                <ListItem style={{ padding: 15 }} onClick={evt => {
                    toggleDrawer();
                    history.push(isAuth ? `${Paths.HomePage}/#contactsec` : `${Paths.MainPage}/#contactsec`);
                }} button>
                    <ListItemText primary={"Contact"} />
                    {/* <ListItemIcon>{<ArrowForwardIosIcon style={{fontSize: 16}} />}</ListItemIcon> */}
                </ListItem>
            </List>
        </div>
    )
};

export default function CategoryDrawer({ list, open, toggleDrawer, handleViewAllClick, isAuth }) {
    return (
        <div>
            <Drawer anchor={'left'} open={open} onClose={toggleDrawer}>
                <DrawerList list={list} isAuth={isAuth} toggleDrawer={toggleDrawer} handleViewAllClick={handleViewAllClick} />
            </Drawer>
        </div>
    );
}