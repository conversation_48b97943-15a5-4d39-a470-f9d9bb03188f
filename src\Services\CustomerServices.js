import axios from 'axios';
import AxiosInterCeptors from './AxiosInterCeptors';
const publicToken = "deviareLg9VwCn6J1lVkPxcAYJqnd-academyADbybpNvp34JSrmnYatHyUme";
const apiUrl = global.platformURI;

export class CustomerServices extends AxiosInterCeptors {
  
  getAllCustomer(url) {
    return axios.get(url).then(res => res.data);
  }

  addCustomer(payload, id) {
    if (id) {
      return axios.put(`main/customerdetail/${id}`, payload)
      .then(res => res.data)
      .catch(err => (err.response && err.response.data) ? err.response.data : err);
    }
    return axios
      .post('main/customercreate', payload)
      .then(res => res.data)
      .catch(err => (err.response && err.response.data) ? err.response.data : err);
  }
  
  sendTestEmail(payload) {
    return axios
      .post('main/testemail', payload)
      .then(res => res.data)
      .catch(err => (err.response && err.response.data) ? err.response.data : err);
  }
  
  getCustomerTheme(id) {
    return axios.get(`main/customertheme/${id}`)
    .then(res => res.data);
  }

  addCustomertheme(payload, uuid) {
    return axios.put(`main/customertheme/${uuid}`, payload)
    .then(res => res.data)
    .catch(err => (err.response && err.response.data) ? err.response.data : err);
  }

  getCustomerDetail(id) {
    return axios
      .get(`main/customerdetail/${id}`)
      .then(res => res.data);
  }

  getCounties() {
    return axios.get(`main/countrylist`).then(res => res.data);
  }
  
  assignCustomerLicence(payload, id) {
    if (id) {
      return axios.put(`main/assign_customer_license?customer_id=${id}`, payload)
      .then(res => res.data)
      .catch(err => (err.response && err.response.data) ? err.response.data : err);
    }
    return axios
      .post('main/assign_customer_license', payload)
      .then(res => res.data)
      .catch(err => (err.response && err.response.data) ? err.response.data : err);
  }

  sendContactMail(payload) {
    const requestOptions = {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': publicToken },
      body: JSON.stringify(payload)
    };
    return fetch(apiUrl + 'notification/contactus/', requestOptions)
      .then(response => response.json())
      .then(data => data)
      .catch(err => (err.response && err.response.data) ? err.response.data : err);
  }
}
