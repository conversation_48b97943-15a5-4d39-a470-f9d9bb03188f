import './App.css';
import React from 'react';
import Routes from './Routes';
import { KeycloakProvider } from './Context/keyCloakProvider';
import { AuthProvider } from './Context/context';
import { muiTheme } from './theme';
import { MuiThemeProvider } from '@material-ui/core/styles';
import CssBaseline from '@material-ui/core/CssBaseline';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './App.css';
import './Sys/App.scss';
import { resetThemeColors } from 'utils';

class App extends React.Component {

  // eslint-disable-next-line no-useless-constructor
  constructor(props) {
    super(props);
  }

  componentDidMount() {
    resetThemeColors();
  }

  render() {
    return (
      <>
        <MuiThemeProvider theme={muiTheme}>
          <CssBaseline />
          <AuthProvider>
            <KeycloakProvider>
              <ToastContainer
                position="top-right"
                autoClose={5000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
              />
              <Routes />
            </KeycloakProvider>
          </AuthProvider>
        </MuiThemeProvider>
      </>
    )
  }
}

export default App;