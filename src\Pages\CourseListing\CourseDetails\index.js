import React, { useEffect, useState } from 'react';
import { useParams, useHistory } from 'react-router-dom';
import { Container, Grid, Typography, <PERSON><PERSON>, Card, CardContent, Divider, CircularProgress, Dialog, IconButton } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import StarBorderIcon from '@material-ui/icons/StarBorder';
import CheckIcon from '@material-ui/icons/Check';
import CloseIcon from '@material-ui/icons/Close';
import Layout from "Components/layouts";
import { useLocation } from 'react-router-dom/cjs/react-router-dom';
import { useKeycloak } from '@react-keycloak/web';

const useStyles = makeStyles((theme) => ({
  root: {
    backgroundColor: '#f9fbfd',
    paddingBottom: theme.spacing(4),
  },
  header: {
    background: 'linear-gradient(70.3deg, rgb(66 147 156) 14.53%, rgb(12, 78, 118) 70.96%)',
    padding: theme.spacing(3, 0),
    color: '#fff',
  },
  breadcrumb: {
    fontSize: '0.9rem',
    opacity: 0.8,
    marginBottom: theme.spacing(1),
  },
  title: {
    fontWeight: 700,
  },
  starRow: {
    display: 'flex',
    gap: 5,
    marginTop: theme.spacing(1),
  },
  section: {
    marginTop: theme.spacing(4),
  },
  courseOutlineItem: {
    backgroundColor: '#f4f6f8',
    padding: theme.spacing(1.5),
    marginBottom: theme.spacing(1),
    borderRadius: 6,
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: '#e8ebee',
    },
  },
  priceCard: {
    padding: theme.spacing(2),
    textAlign: 'center',
    border: '1px solid #ddd',
    borderRadius: 8,
    marginBottom: theme.spacing(2),
    width: '100%',
    maxWidth: 350,
    margin: '0 auto 16px auto',
  },
  price: {
    fontSize: '1.5rem',
    fontWeight: 600,
    margin: theme.spacing(1, 0),
  },
  btnPrimary: {
    backgroundColor: '#497ec4',
    color: '#fff',
    marginTop: theme.spacing(1),
    '&:hover': {
      backgroundColor: '#125ea2',
    },
    borderRadius: 8,
    padding: theme.spacing(1.5, 3),
    whiteSpace: 'nowrap',
    width: '80%',
    fontWeight: 500,
  },
  checkItem: {
    display: 'flex',
    alignItems: 'center',
    gap: 6,
    marginBottom: theme.spacing(1),
  },
  subHeading: {
    fontWeight: 600,
    marginBottom: theme.spacing(1),
  },
  rightSectionContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
  },
  // Iframe modal styles
  iframeDialog: {
    '& .MuiDialog-paper': {
      margin: 0,
      width: '100vw',
      height: '100vh',
      maxWidth: 'none',
      maxHeight: 'none',
      borderRadius: 0,
    },
  },
  iframeContainer: {
    position: 'relative',
    width: '100%',
    height: '100vh',
    display: 'flex',
    flexDirection: 'column',
  },
  iframeHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(1, 2),
    backgroundColor: '#f5f5f5',
    borderBottom: '1px solid #ddd',
    minHeight: 56,
  },
  iframeTitle: {
    fontWeight: 600,
    color: '#333',
  },
  closeButton: {
    color: '#666',
  },
  iframe: {
    flex: 1,
    width: '100%',
    border: 'none',
    backgroundColor: '#fff',
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    fontSize: '18px',
    color: '#666',
  },
}));

const CourseDetails = () => {
  // State for expanded course outline sections
  const [outlineOpen, setOutlineOpen] = useState([]);
  const classes = useStyles();
  const query = new URLSearchParams(useLocation().search);
  const [course, setCourse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [profileData, setProfileData] = useState(null);
  const [iframeOpen, setIframeOpen] = useState(false);
  const [iframeUrl, setIframeUrl] = useState('');
  const [iframeLoading, setIframeLoading] = useState(false);
  const id = query.get("course_id");
  const { keycloak } = useKeycloak();
  const history = useHistory();
  
  // Simplified login check
  const isLoggedIn = Boolean(
    localStorage.getItem('token') || 
    keycloak?.authenticated
  );

  // Fetch user profile data on component mount
  useEffect(() => {
    if (isLoggedIn) {
      fetchProfileData();
    }
  }, [isLoggedIn]);

  const fetchProfileData = async () => {
    try {
      const response = await fetch('https://api-staging.deviare.africa/main/myprofile', {
        method: 'GET',
        headers: {
          'accept': 'application/json, text/plain, */*',
          'authorization': `Token ${localStorage.getItem('token')}`,
        }
      });
      
      const data = await response.json();
      
      if (data.status && data.data && data.data.length > 0) {
        setProfileData(data.data[0]);
        console.log('Profile data loaded:', data.data[0]);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  // Updated checkCustomerCourses - only used for Free Trial button
  const checkCustomerCoursesForFreeTrial = async (userId) => {
    try {
      console.log(`Checking customer courses for user ID: ${userId}`);
      const response = await fetch(`https://api-staging.deviare.africa/main/CustomerCourses/${userId}`, {
        method: 'GET',
        headers: {
          'accept': 'application/json',
          'authorization': `Token ${localStorage.getItem('token')}`,
          'content-type': 'application/json',
        }
      });
      const data = await response.json();
      console.log('Customer courses API response:', data);
      if (data.success && data.data && data.data.courses && data.data.courses.length > 0) {
        console.log('User has existing courses, redirecting to /account/course');
        history.push('/account/course');
        return true;
      }
      console.log('User has no existing courses');
      return false;
    } catch (error) {
      console.error('Error checking customer courses:', error);
      return false;
    }
  };

  // Function to open subscription iframe
  const openSubscriptionIframe = (planId, planType = 'lifetime') => {
    if (!profileData) {
      alert('Please log in to continue with the purchase.');
      return;
    }

    const firstName = profileData.firstName || profileData.first_name || 'User';
    const lastName = profileData.lastName || profileData.last_name || '';
    const email = profileData.email || '';

    if (!email) {
      alert('Email not found. Please update your profile and try again.');
      return;
    }

    // Build subscription URL with plan_id and course_id
    const subscriptionUrl = `https://deviare.subscriptionflow.com/en/hosted-page/subscribe/${planId}/product/${id}?ai_firstName=${encodeURIComponent(firstName)}&ai_lastName=${encodeURIComponent(lastName)}&ai_email=${encodeURIComponent(email)}`;
    
    console.log(`Opening ${planType} subscription URL in iframe:`, subscriptionUrl);
    setIframeUrl(subscriptionUrl);
    setIframeLoading(true);
    setIframeOpen(true);
  };

  // Updated handleBuyCourse - always open iframe, no course checking
  const handleBuyCourse = async () => {
    console.log('Buy course clicked, isLoggedIn:', isLoggedIn);
    
    if (!isLoggedIn) {
      console.log('User not logged in, redirecting to Keycloak login');
      keycloak.login();
      return;
    }

    // Check if lifetime plan exists in pricing data
    if (course?.pricing?.lifetime_plan?.plan_id) {
      // Always open iframe with lifetime plan - no course checking for purchase
      openSubscriptionIframe(course.pricing.lifetime_plan.plan_id, 'lifetime');
    } else {
      console.error('Lifetime plan not found in course pricing data');
      alert('Lifetime purchase option is not available for this course.');
    }
  };

  // Updated handleFreeTrial - only this function checks existing courses
  const handleFreeTrial = async () => {
    console.log('Free trial clicked, isLoggedIn:', isLoggedIn);
    
    if (!isLoggedIn) {
      console.log('User not logged in, redirecting to Keycloak login');
      keycloak.login();
      return;
    }

     history.push(`/plans?course_id=${id}`);
  };

  // Handle iframe load
  const handleIframeLoad = () => {
    setIframeLoading(false);
  };

  // Handle closing iframe
  const handleCloseIframe = () => {
    setIframeOpen(false);
    setIframeUrl('');
    setIframeLoading(false);
  };

  // Listen for messages from iframe for subscription completion
  useEffect(() => {
    const handleMessage = (event) => {
      // Security check - only accept messages from subscription domain
      if (!event.origin.includes('deviare.subscriptionflow.com')) {
        return;
      }
      
      console.log('Received message from iframe:', event.data);
      
      // Handle subscription success
      if (event.data && event.data.type === 'subscription_success') {
        console.log('Subscription successful!');
        handleCloseIframe();
        alert('Purchase completed successfully! You now have access to the course.');
        // Optionally redirect to course or account page
        // history.push('/account/course');
      }
      
      // Handle subscription cancellation
      if (event.data && event.data.type === 'subscription_cancelled') {
        console.log('Subscription cancelled');
        handleCloseIframe();
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [history]);

  useEffect(() => {
    console.log(`Fetching course details for ID: ${id}`);
    fetch(`https://api-staging.deviare.africa/main/coursedetails/${id}`)
      .then(res => res.json())
      .then(data => {
        console.log('Course data received:', data);
        if (data.status && data.data) {
          setCourse(data.data);
        } else {
          setError('Failed to load course details');
        }
        setLoading(false);
      })
      .catch((error) => {
        console.error('Course fetch error:', error);
        setError('Failed to load course details');
        setLoading(false);
      });
  }, [id]);

  if (loading) return <CircularProgress style={{ display: 'block', margin: 'auto', marginTop: '20%' }} />;
  
  if (error || !course) {
    return (
      <Layout>
        <Container>
          <Typography color="error" align="center" style={{ marginTop: '20%' }}>
            {error || 'Course not found'}
          </Typography>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className={classes.root}>
        {/* Header */}
        <div className={classes.header} style={{height: '300px', display: 'flex', alignItems: 'center'}}>
          <Container>
            <Typography variant="h3" className={classes.title} style={{color: "white", textAlign: "center"}}>
              {course.name || 'Course Title'}
            </Typography>
            <div className={classes.starRow} style={{justifyContent: 'center'}}>
              {[...Array(5)].map((_, i) => (
                <StarBorderIcon key={i} style={{ color: '#fff' }} />
              ))}
            </div>
          </Container>
        </div>

        <Container>
          <Grid container spacing={4} className={classes.section}>
            {/* Left Section */}
            <Grid item xs={12} md={8}>
              <Typography variant="h6" style={{ fontWeight: 600, marginBottom: 8, color:"#4d81bf" }}>
                Course Description
              </Typography>
              <Typography variant="body1" paragraph>
                {course.course_description || 'Course description will be available soon.'}
              </Typography>

              {/* Key Learning Objectives */}
              <Typography variant="h6" className={classes.subHeading} style={{color:'#4d81bf', marginTop: 50}}>
                Key Learning Objectives
              </Typography>
              {Array.isArray(course.course_objectives) ? (
                <Grid container spacing={2}>
                  {course.course_objectives
                    .flatMap(obj => obj.split('•').map(s => s.trim()).filter(Boolean))
                    .map((objective, idx) => (
                      <Grid item xs={12} sm={6} key={idx}>
                        <div className={classes.checkItem}>
                          <CheckIcon style={{ color: '#4d81bf', fontSize: 18 }} />
                          <Typography variant="body2">{objective}</Typography>
                        </div>
                      </Grid>
                    ))}
                </Grid>
              ) : (
                <Typography variant="body2" paragraph>
                  {course.course_objectives || 'Learning objectives will be available soon.'}
                </Typography>
              )}

              {/* Course Outline */}
              <Typography variant="h6" className={classes.subHeading} style={{color:'#4d81bf', marginTop: 50}}>
                Course Outline
              </Typography>
              {Array.isArray(course.course_outline) ? (
                course.course_outline.map((section, idx) => {
                  const [title, ...rest] = section.split('•');
                  const points = rest.map(s => s.trim()).filter(Boolean);
                  const open = outlineOpen[idx] || false;
                  const handleToggle = () => {
                    setOutlineOpen(prev => {
                      const copy = [...prev];
                      copy[idx] = !copy[idx];
                      return copy;
                    });
                  };
                  return (
                    <div key={idx} style={{marginBottom: 12}}>
                      <div
                        className={classes.courseOutlineItem}
                        style={{cursor: points.length ? 'pointer' : 'default', display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}
                        onClick={() => points.length && handleToggle()}
                      >
                        <Typography variant="subtitle1" style={{fontWeight: 600}}>{title}</Typography>
                        {points.length > 0 && (
                          <span style={{fontSize: 18}}>{open ? '▲' : '▼'}</span>
                        )}
                      </div>
                      {open && points.length > 0 && (
                        <ul style={{marginLeft: 24, marginTop: 8}}>
                          {points.map((point, i) => (
                            <li key={i}>
                              <Typography variant="body2">{point}</Typography>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  );
                })
              ) : (
                <Typography variant="body2">
                  {course.course_outline || 'Course outline will be available soon.'}
                </Typography>
              )}

              {/* Intended Audience */}
              <Typography variant="h6" className={classes.subHeading} style={{color:'#4d81bf', marginTop: 50}}>
                Intended Audience
              </Typography>
              {Array.isArray(course.intended_audience) ? (
                <Grid container spacing={2}>
                  {course.intended_audience
                    .flatMap(aud => aud.split('•').map(s => s.trim()).filter(Boolean))
                    .map((aud, idx) => (
                      <Grid item xs={12} sm={6} key={idx}>
                        <div className={classes.checkItem}>
                          <CheckIcon style={{ color: '#4d81bf', fontSize: 18 }} />
                          <Typography variant="body2">{aud}</Typography>
                        </div>
                      </Grid>
                    ))}
                </Grid>
              ) : (
                <Typography variant="body2">
                  {course.intended_audience || 'Intended audience information will be available soon.'}
                </Typography>
              )}

              {/* Prerequisites */}
              <Typography variant="h6" className={classes.subHeading} style={{color:"#4d81bf", marginTop: 50}}>
                Prerequisites
              </Typography>
              <Typography variant="body2">
                {course.prerequisites || 'Prerequisites information will be available soon.'}
              </Typography>
            </Grid>

            {/* Right Section - ALWAYS VISIBLE */}
            <Grid item xs={12} md={4}>
              <div className={classes.rightSectionContainer}>
                <Typography variant="h6" style={{ marginBottom: 16, fontWeight: 600, textAlign: 'center' }}>
                  Access how you want
                </Typography>

                {/* Flexible Card - ALWAYS SHOW */}
                <Card className={classes.priceCard}>
                  <CardContent>
                    <Typography variant="h6" className={classes.price}>
                      Flexible
                    </Typography>
                    <Typography>
                      {course.pricing?.monthly_plan?.total_amount || 'R 99.00'} / month
                    </Typography>
                    <Typography variant="body2" paragraph>
                      Gain unlimited access to this course and our entire library of premium content with a low monthly payment.
                    </Typography>
                    <Button 
                      className={classes.btnPrimary} 
                      onClick={handleFreeTrial}
                      variant="contained"
                    >
                      Start Your Free Trial
                    </Button>
                  </CardContent>
                </Card>

                <Typography align="center" style={{ marginBottom: 16 }}>
                  Or
                </Typography>

                {/* Lifetime Ownership Card - ALWAYS SHOW */}
                <Card className={classes.priceCard}>
                  <CardContent>
                    <Typography variant="h6" className={classes.price}>
                      Lifetime Ownership
                    </Typography>
                    <Typography>
                      {course.pricing?.lifetime_plan?.total_amount || course.pricing?.price || 'R 345.00'}
                    </Typography>
                    <Typography variant="body2" paragraph>
                      Gain unlimited access to this course and our entire library of premium content with a one-time payment.
                    </Typography>
                    <Button 
                      className={classes.btnPrimary} 
                      onClick={handleBuyCourse}
                      variant="contained"
                      disabled={!course.pricing?.lifetime_plan?.plan_id}
                    >
                      {course.pricing?.lifetime_plan?.plan_id ? 'Buy Course Now' : 'Not Available'}
                    </Button>
                  </CardContent>
                </Card>

                {/* Launch Course Button - Show if link exists */}
                {course.link && (
                  <div style={{marginTop: 16, textAlign: 'center'}}>
                    <Button 
                      variant="outlined" 
                      color="primary" 
                      href={course.link} 
                      target="_blank"
                      style={{ minWidth: 150 }}
                    >
                      Launch Course
                    </Button>
                  </div>
                )}
              </div>
            </Grid>
          </Grid>
        </Container>

        {/* Full Screen Iframe Dialog */}
        <Dialog
          open={iframeOpen}
          onClose={handleCloseIframe}
          className={classes.iframeDialog}
          fullScreen
        >
          <div className={classes.iframeContainer}>
            <div className={classes.iframeHeader}>
              <Typography className={classes.iframeTitle}>
                Complete Your Purchase - {course.pricing?.lifetime_plan?.plan_name || 'Lifetime Access'}
              </Typography>
              <IconButton 
                className={classes.closeButton}
                onClick={handleCloseIframe}
                aria-label="close"
              >
                <CloseIcon />
              </IconButton>
            </div>
            {iframeLoading && (
              <div className={classes.loadingContainer}>
                Loading purchase page...
              </div>
            )}
            {iframeUrl && (
              <iframe
                src={iframeUrl}
                className={classes.iframe}
                title="Purchase Page"
                onLoad={handleIframeLoad}
                allow="payment; camera; microphone"
                sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
              />
            )}
          </div>
        </Dialog>
      </div>
    </Layout>
  );
};

export default CourseDetails;
