export const ERROR_MESSAGE = {
    REQUIRED_LOGIN: "Please enter your Username and Password",
    PASSWORD_MATCH: "Password and Confirm password should be same!",
    VALID_EMAIL: "Please enter your valid email address",
    VALID_PASSWORD: "Password must be more then 5 digit",
    INVALID_CRED: "Invalid login credentials!",
}
export const REQUIRED_ERROR = "Fields marked as * are required.";
export const SUCCESS_MESSAGE = {
    LOGIN_SUCCESS: "Login successfully!"
}
export const USER_ROLES = {
    SUPER_ADMIN: "superadmin",
    USER: "user",
    CUSTOMER_ADMIN: "customeradmin",
    PROJECT_ADMIN: "projectadmin"
}

export const analyticsColumns = [
    {
        label: 'Username',
        key: 'Username',
        sortType: 'basic'
    },
    {
        label: 'Learner Email',
        key: 'Learner Email',
        sortType: 'basic'
    },
    {
        label: 'Order Type',
        key: 'order_type',
        sortType: 'basic'
    },
    {
        label: 'Activity Level',
        key: 'activity_level',
        sortType: 'basic'
    },
    {
        label: 'Cohort',
        key: 'cohort',
        sortType: 'basic'
    },
    {
        label: 'Course Id',
        key: 'course_id',
        sortType: 'basic'
    },
    {
        label: 'Course Name',
        key: 'course_title',
        sortType: 'basic'
    },
    {
        label: 'Course Assignment Date',
        key: 'course_assignment_date',
        sortType: 'basic'
    },
    {
        label: 'Course Completion Date',
        key: 'course_completion_date',
        sortType: 'basic'
    },
    {
        label: 'Course Activation Date',
        key: 'course_activation_date',
        sortType: 'basic'
    },
    {
        label: 'Course Expiration Date',
        key: 'course_expiration_date',
        sortType: 'basic'
    },
    {
        label: 'Course Type',
        key: 'course_type',
        sortType: 'basic'
    },
    {
        label: 'Self-Learning Completion',
        key: 'self_learning_completion',
        sortType: 'basic'
    },
    {
        label: 'Assessment Test',
        key: 'Assessment Test',
        sortType: 'basic'
    },
    {
        label: 'Project Status',
        key: 'Project Status',
        sortType: 'basic'
    },
    {
        label: 'Live Class Attended',
        key: 'live_class_attended',
        sortType: 'basic'
    },
    {
        label: 'OSL Score',
        key: 'osl_score',
        sortType: 'basic'
    },
    {
        label: 'LVC Score',
        key: 'lvc_sore',
        sortType: 'basic'
    },
    {
        label: 'Certification Status',
        key: 'certification_status',
        sortType: 'basic'
    },
    {
        label: 'Last Login Date',
        key: 'last_login_date',
        sortType: 'basic'
    },
    {
        label: 'Last Activity on',
        key: 'last_activity_on',
        sortType: 'basic'
    },
    {
        label: 'Self Learning Time',
        key: 'self_learning_time',
        sortType: 'basic'
    },
    {
        label: 'Overall course completion score',
        key: 'overall_course_completion_score',
        sortType: 'basic'
    }
]

export const overViewColumn = [
    {
        label: 'Username',
        key: 'Username',
        sortType: 'basic'
    },
    {
        label: 'Course Name',
        key: 'course_title',
        sortType: 'basic'
    },
    {
        label: 'Activity Level',
        key: 'activity_level',
        sortType: 'basic'
    },
    {
        label: 'Certification Status',
        key: 'certification_status',
        sortType: 'basic'
    },
    {
        label: 'Course Assignment Date',
        key: 'assign_date',
        sortType: 'basic'
    },
    {
        label: 'Course Completion Date',
        key: 'completion_date',
        sortType: 'basic'
    }
]

export const progressColumn = [
    {
        label: 'Username',
        key: 'Username',
        sortType: 'basic'
    },
    {
        label: 'Course Name',
        key: 'course',
        sortType: 'basic'
    },
    {
        label: 'Activity Level',
        key: 'activity_level',
        sortType: 'basic'
    },
    {
        label: 'Assessment Test',
        key: 'Assessment Test',
        sortType: 'basic'
    },
    {
        label: 'Self-Learning Completion',
        key: 'self_learning',
        sortType: 'basic'
    },
    {
        label: 'Live Class Attended',
        key: 'live_class',
        sortType: 'basic'
    },
    {
        label: 'Project Status',
        key: 'Project Status',
        sortType: 'basic'
    },
    {
        label: 'Overall course completion score',
        key: 'overall_course_completion_score',
        sortType: 'basic'
    },
    {
        label: 'Certification Status',
        key: 'certification_status',
        sortType: 'basic'
    }
]

export const RespondentColumn = [
    {
        label: 'Customer',
        key: 'customer_name',
        sortType: 'basic'
    },
    {
        label: 'Project',
        key: 'project_name',
        sortType: 'basic'
    },
    {
        label: 'Name',
        key: 'name',
        sortType: 'basic'
    },
    {
        label: 'Surname',
        key: 'surname',
        sortType: 'basic'
    },
    {
        label: 'Username',
        key: 'email',
        sortType: 'basic'
    },
    {
        label: 'Customer Dimension',
        key: 'customer_dim',
        sortType: 'basic'
    },
    {
        label: 'Strategy Dimension',
        key: 'strategy_dim',
        sortType: 'basic'
    },
    {
        label: 'Technology Dimension',
        key: 'technology_dim',
        sortType: 'basic'
    },
    {
        label: 'Operations Dimension',
        key: 'operations_dim',
        sortType: 'basic'
    },
    {
        label: 'Culture Dimension',
        key: 'culture_dim',
        sortType: 'basic'
    },
    {
        label: 'Data Dimension',
        key: 'data_dim',
        sortType: 'basic'
    }
]

export const analyticsArr = [
    'Select Customer',
    'Select Certificate',
    'Select Project',
    'Select Courses'
]

export const overviewArr = [
    'Select Customer',
    'Select Certificate',
    'Select Team',
    'Select Courses'
]

export const progressArr = [
    'Select Customer',
    'Select Certificate',
    'Select Project',
    'Select Courses'
]

export const CustomerDimentionDesc = `The Customer dimension evaluates the provision of an engaging 
experience where customers view the organization as their digital partner using their preferred 
channels of interaction.`

export const StrategyDimentionDesc = `The Strategy Dimension evaluates how well the business plans to 
increase its competitive advantage through a comprehensive digital strategy and designs a set of 
initiatives that support the overall business strategy.`

export const TechnologyDimentionDesc = `The Technology dimension evaluates the technology capabilities
 of the organization to establish, maintain and continually transform an environment that supports the
 delivery of business objectives.`

export const OperationDimentionDesc = `The Operations dimension evaluates the organization's performance of 
day-to-day activities that support the execution of the digital strategy.`

export const CultureDimentionDesc = `The Culture dimension evaluates the ability of an organization to create
an environment where everyone is willing and able to create business value.`

export const DataDimentionDesc = `The Data dimension evaluates the organization's ability both strategically 
and operationally to ethically and effectively use data and information assets to maximize business
value.`

export const ClosingDesc = `The Gap is difference between your company's status quo aspiration 
responses from the digital readiness assessment. Engage our experts to leverage the Digital Readiness 
Assessment and advance your organization's digital transformation journey. 
Contact <EMAIL>.`

export const customerFactor = [
    'Customer outside-in view',
    'Customer experience management',
    'Customer insights',
    'Customer trust'
]

export const strategyFactor = [
    'Marketing & brand management',
    'Ecosystem management',
    'Financial sponsorship',
    'Market intelligence',
    'Portfolio management',
    'Strategy management'
]

export const technologyFactor = [
    'Technology governance',
    'Technology & application architecture',
    'Security',
    'Applications & platforms',
    'Connectivity & compute'
]

export const operationFactor = [
    'Operations governance',
    'Service design & innovation',
    'Service transition/deployment',
    'Service operations'
]

export const cultureFactor = [
    'Organisational values',
    'Talent management',
    'Workplace enablement'
]

export const dataFactor = [
    'Data governance',
    'Data engineering',
    'Data value realisation'
]

export const AssesmentsSummary = `Digital transformation begins with understanding your organisation’s capacity and capability for digital advancement. These set of assessments lay the foundation for your company’s digital transformation journey.`

export const DurationList = [
    '15 Minutes',
    '30 Minutes',
    '45 Minutes',
    '1 Hour'
]

export const RepeatMode = [
    "Daily",
    "Weekly",
    "Monthly"
]

export const DaysList = [
    'Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'
] 

export const Industries = [
  "Accounting",
  "Airlines/Aviation",
  "Alternative Dispute Resolution",
  "Alternative Medicine",
  "Animation",
  "Apparel & Fashion",
  "Architecture & Planning",
  "Arts & Crafts",
  "Automotive",
  "Aviation & Aerospace",
  "Banking",
  "Biotechnology",
  "Broadcast Media",
  "Building Materials",
  "Business Supplies & Equipment",
  "Capital Markets",
  "Chemicals",
  "Civic & Social Organization",
  "Civil Engineering",
  "Commercial Real Estate",
  "Computer & Network Security",
  "Computer Games",
  "Computer Hardware",
  "Computer Networking",
  "Computer Software",
  "Construction",
  "Consumer Electronics",
  "Consumer Goods",
  "Consumer Services",
  "Cosmetics",
  "Dairy",
  "Defense & Space",
  "Design",
  "Education Management",
  "E-learning",
  "Electrical & Electronic Manufacturing",
  "Entertainment",
  "Environmental Services",
  "Events Services",
  "Executive Office",
  "Facilities Services",
  "Farming",
  "Financial Services",
  "Fine Art",
  "Fishery",
  "Food & Beverages",
  "Food Production",
  "Fundraising",
  "Furniture",
  "Gambling & Casinos",
  "Glass, Ceramics & Concrete",
  "Government Administration",
  "Government Relations",
  "Graphic Design",
  "Health, Wellness & Fitness",
  "Higher Education",
  "Hospital & Health Care",
  "Hospitality",
  "Human Resources",
  "Import & Export",
  "Individual & Family Services",
  "Industrial Automation",
  "Information Services",
  "Information Technology & Services",
  "Insurance",
  "International Affairs",
  "International Trade & Development",
  "Internet",
  "Investment Banking/Venture",
  "Investment Management",
  "Judiciary",
  "Law Enforcement",
  "Law Practice",
  "Legal Services",
  "Legislative Office",
  "Leisure & Travel",
  "Libraries",
  "Logistics & Supply Chain",
  "Luxury Goods & Jewelry",
  "Machinery",
  "Management Consulting",
  "Maritime",
  "Marketing & Advertising",
  "Market Research",
  "Mechanical or Industrial Engineering",
  "Media Production",
  "Medical Device",
  "Medical Practice",
  "Mental Health Care",
  "Military",
  "Mining & Metals",
  "Motion Pictures & Film",
  "Museums & Institutions",
  "Music",
  "Nanotechnology",
  "Newspapers",
  "Nonprofit Organization Management",
  "Oil & Energy",
  "Online Publishing",
  "Outsourcing/Offshoring",
  "Package/Freight Delivery",
  "Packaging & Containers",
  "Paper & Forest Products",
  "Performing Arts",
  "Pharmaceuticals",
  "Philanthropy",
  "Photography",
  "Plastics",
  "Political Organization",
  "Primary/Secondary",
  "Printing",
  "Professional Training",
  "Program Development",
  "Public Policy",
  "Public Relations",
  "Public Safety",
  "Publishing",
  "Railroad Manufacture",
  "Ranching",
  "Real Estate",
  "Recreational",
  "Facilities & Services",
  "Religious Institutions",
  "Renewables & Environment",
  "Research",
  "Restaurants",
  "Retail",
  "Security & Investigations",
  "Semiconductors",
  "Shipbuilding",
  "Sporting Goods",
  "Sports",
  "Staffing & Recruiting",
  "Supermarkets",
  "Telecommunications",
  "Textiles",
  "Think Tanks",
  "Tobacco",
  "Translation & Localization",
  "Transportation/Trucking/Railroad",
  "Utilities",
  "Venture Capital",
  "Veterinary",
  "Warehousing",
  "Wholesale",
  "Wine & Spirits",
  "Wireless",
  "Writing & Editing"
];

export const PAYGATE_END_POINTS = {
    INIT: 'https://secure.paygate.co.za/payweb3/initiate.trans',
    REDIRECT: 'https://secure.paygate.co.za/payweb3/process.trans',
    QUERY: 'https://secure.paygate.co.za/payweb3/query.trans'
}

export const OCCUPATIONS = [
      "Accountant",
      "Actor",
      "Actuary",
      "Administrative Assistant",
      "Agricultural Worker",
      "Air Traffic Controller",
      "Aircraft Pilot",
      "Animator",
      "Architect",
      "Artist",
      "Astronomer",
      "Athlete",
      "Author",
      "Baker",
      "Banker",
      "Barber",
      "Bartender",
      "Biologist",
      "Botanist",
      "Bricklayer",
      "Carpenter",
      "Cashier",
      "Chef",
      "Chemist",
      "Civil Engineer",
      "Cleaner",
      "Clergy",
      "Clinical Psychologist",
      "Computer Programmer",
      "Construction Worker",
      "Counselor",
      "Dancer",
      "Dentist",
      "Designer",
      "Detective",
      "Dietitian",
      "Doctor",
      "Economist",
      "Editor",
      "Electrician",
      "Engineer",
      "Environmental Scientist",
      "Event Planner",
      "Fashion Designer",
      "Film Director",
      "Financial Advisor",
      "Firefighter",
      "Fisherman",
      "Flight Attendant",
      "Florist",
      "Forensic Scientist",
      "Game Developer",
      "Gardener",
      "Geologist",
      "Hair Stylist",
      "Historian",
      "Hotel Manager",
      "Human Resources Manager",
      "Illustrator",
      "Information Security Analyst",
      "Interpreter",
      "Investment Banker",
      "Journalist",
      "Judge",
      "Landscaper",
      "Librarian",
      "Lifeguard",
      "Linguist",
      "Makeup Artist",
      "Manager",
      "Marine Biologist",
      "Marketing Manager",
      "Mathematician",
      "Mechanic",
      "Medical Doctor",
      "Meteorologist",
      "Model",
      "Musician",
      "Nanny",
      "Nurse",
      "Nutritionist",
      "Optometrist",
      "Painter",
      "Paramedic",
      "Pharmacist",
      "Photographer",
      "Physical Therapist",
      "Physician Assistant",
      "Physicist",
      "Plumber",
      "Police Officer",
      "Politician",
      "Professor",
      "Psychiatrist",
      "Psychologist",
      "Public Relations Specialist",
      "Real Estate Agent",
      "Receptionist",
      "Research Scientist",
      "Salesperson",
      "Scientist",
      "Sculptor",
      "Social Worker",
      "Software Developer",
      "Speech Therapist",
      "Surgeon",
      "Taxi Driver",
      "Teacher",
      "Translator",
      "Travel Agent",
      "Truck Driver",
      "Urban Planner",
      "Veterinarian",
      "Video Game Designer",
      "Waiter/Waitress",
      "Web Developer",
      "Writer"
];

export const COMPANY_DETAILS = {
    name: "Deviare (Pty) Ltd",
    address: "Block C, Cedar Tree Medical and Office Park,",
    address2: "Fourways, Sandton, Gauteng, 2055",
    phoneNo: "+27 10 595 8522",
    email: "<EMAIL>",
    vatRegistrationNo: "**********",
    businessIdNo: "2017/263701/07" 
}