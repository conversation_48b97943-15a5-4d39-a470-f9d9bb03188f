import React from 'react';
import {Text, View, StyleSheet } from '@react-pdf/renderer';
import moment from 'moment';

const styles = StyleSheet.create({
    container: {
      marginTop: 0,
      marginBottom: 0,
      paddingBottom: 0,
      flexDirection: 'row',
      '@media max-width: 400': {
          flexDirection: 'column',
      },
    },
    leftColumn: {
        flexDirection: 'column',
        width: 360,
        paddingTop: 40,
        '@media max-width: 400': {
          width: '100%',
          paddingRight: 0,
        },
        '@media orientation: landscape': {
          width: 200,
        },
    },
    rightContainer: {
        flexDirection: 'column',
        paddingTop: 40,
        paddingLeft: 0,
        paddingBottom: 0,
        '@media max-width: 400': {
            paddingTop: 10,
            paddingLeft: 0,
        },
    },
  
});

const BillTo = ({ userInfo }) => (
  <View style={styles.container}>
    <View style={styles.leftColumn}>
        <Text>{`${userInfo.name}`}</Text>
        <Text>{userInfo.address1}, {userInfo.state}</Text>
        <Text>{userInfo.city}, {userInfo.pinCode}, {userInfo.country}</Text>
        <Text>{userInfo.contact_no}</Text>
        <Text>{userInfo.email}</Text>
    </View>
    <View style={styles.rightContainer}>
      <Text>INVOICE: {userInfo?.invoiceId || "-"}</Text>
      <Text>DATE: {userInfo?.transaction_date ? moment(userInfo.transaction_date).format('YYYY.MM.DD') : '-'}</Text>
    </View>
  </View>
);
  
  export default BillTo  