// Pages/Profile/ProfileTabs.js
import React from "react";
import { NavLink, Route, Redirect } from "react-router-dom";
import StartLearningProfile from "../StartLearningProfile";
import StartLearningChangePassword from "../StartLearningChangePassword";
import Layout from "Components/layouts";
import Courses from "../CourseLearning/index";
import BillingPage from "../BillingPage/index";

const StartLearning = () => {
  return (
    <Layout sidebarView={false}>
      <style>
        {`
          .tab-container {
            display: flex;
            gap: 10px;
            background: #c9c9c9; /* grey background like screenshot */
            padding: 8px;
            border-radius: 8px;
            margin-bottom: 20px;
          }

          .tab-link {
            padding: 8px 18px;
            border-radius: 6px;
            text-decoration: none;
            color: #555;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.2s ease;
          }

          .tab-link:hover {
            background: #e0e0e0;
            color: #000;
          }

          .active-tab {
            background: #fff;
            font-weight: bold;
            color: #000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
        `}
      </style>

      {/* ✅ Tab Navigation */}
      <div className="tab-container">
        <NavLink to="/account/profile" activeClassName="active-tab" className="tab-link">
          Profile
        </NavLink>
        <NavLink to="/account/billing" activeClassName="active-tab" className="tab-link">
          Billing
        </NavLink>
        <NavLink to="/account/courses" activeClassName="active-tab" className="tab-link">
          Courses
        </NavLink>
        <NavLink to="/account/password" activeClassName="active-tab" className="tab-link">
          Password
        </NavLink>
      </div>

      {/* ✅ Tab Content */}
      <div style={{ marginTop: "20px" }}>
        <Route path="/account/profile" component={StartLearningProfile} />
        <Route path="/account/billing" component={BillingPage} />
        <Route path="/account/courses" component={Courses} />
        <Route path="/account/password" component={StartLearningChangePassword} />
        <Redirect from="/account" to="/account/courses" />
      </div>
    </Layout>
  );
};

export default StartLearning;
