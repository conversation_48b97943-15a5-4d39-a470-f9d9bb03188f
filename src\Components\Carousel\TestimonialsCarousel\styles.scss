.swiper {
    width: 100%;
    height: 100%;
}

.swiper-slide {
    font-size: 18px;
    /* Center slide text vertically */
    display: flex;
    justify-content: center;
    align-items: center;
}

.swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.Testimonial {
    &__Carousel {
        position: relative;
        grid-column: span 12;
        margin-top: 20px;
        width: 100%;
    }
    &__Navigation {
        text-align: center;
        &__button {
            padding-right: 35px;
            &--disabled {
                color: rgba(128, 128, 128, 0.596);
            }

            &--hidden {
                display: none;
            }

            &:focus-visible {
                outline: thin auto Highlight;
                outline: thin auto -webkit-focus-ring-color;
            }
        }
    }
}
.Testimonial__Carousel .swiper-pagination-progressbar span.swiper-pagination-progressbar-fill {
    background-color: #5dd0df
}